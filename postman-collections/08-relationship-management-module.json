{"module_info": {"name": "关系管理模块", "description": "角色间关系的完整管理，构建角色关系网络", "route_prefix": "/api/relationships", "dependencies": ["认证管理模块", "小说管理模块", "角色管理模块", "关系类型模块"]}, "folder": {"name": "关系管理模块", "description": "### 关系管理模块\n\n#### 业务逻辑\n- 角色间关系的完整CRUD操作\n- 支持有向和无向关系定义\n- 关系类型的分类和可视化\n- 关系网络的构建和分析\n- 关系描述和详细信息管理\n- 关系在图谱中的展示和交互\n\n#### 业务约束\n- 关系必须在同一小说的角色间建立\n- 用户只能管理自己小说中的关系\n- 关系的源角色和目标角色不能相同\n- 必须指定有效的关系类型\n- 同一对角色间可以有多种不同类型的关系\n\n#### 权限控制\n- 需要登录才能访问所有接口\n- 用户只能操作自己小说中的关系\n- 管理员可以查看和管理所有关系", "item": [{"name": "获取小说的所有关系", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationships/{{novel_id}}", "variable": [{"key": "novel_id", "value": "1", "description": "小说ID"}]}, "description": "#### 获取小说的所有关系\n\n**功能说明**: 获取指定小说中所有角色间的关系列表\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能访问自己小说的关系\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 小说不存在 | 检查小说ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的小说 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回指定小说中的关系\n- 包含关系的详细信息和关联数据\n- 按创建时间倒序排列\n- 用户只能查看自己小说的关系"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationships/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"sourceId\": 1,\n    \"targetId\": 2,\n    \"typeId\": 3,\n    \"description\": \"萧炎和薰儿从小一起长大，是青梅竹马的关系\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T12:00:00.000Z\",\n    \"sourceCharacter\": {\n      \"id\": 1,\n      \"name\": \"萧炎\",\n      \"avatar\": \"/uploads/character_abc123.jpg\"\n    },\n    \"targetCharacter\": {\n      \"id\": 2,\n      \"name\": \"薰儿\",\n      \"avatar\": \"https://example.com/xuner-avatar.jpg\"\n    },\n    \"relationshipType\": {\n      \"id\": 3,\n      \"name\": \"恋人\",\n      \"color\": \"#E91E63\"\n    }\n  },\n  {\n    \"id\": 2,\n    \"sourceId\": 1,\n    \"targetId\": 3,\n    \"typeId\": 10,\n    \"description\": \"药老是萧炎的师父，传授他炼药术和斗技\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T13:00:00.000Z\",\n    \"sourceCharacter\": {\n      \"id\": 1,\n      \"name\": \"萧炎\",\n      \"avatar\": \"/uploads/character_abc123.jpg\"\n    },\n    \"targetCharacter\": {\n      \"id\": 3,\n      \"name\": \"药老\",\n      \"avatar\": null\n    },\n    \"relationshipType\": {\n      \"id\": 10,\n      \"name\": \"师徒\",\n      \"color\": \"#9C27B0\"\n    }\n  }\n]", "description": "### 获取关系列表成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 关系唯一标识 |\n| sourceId | number | 源角色ID |\n| targetId | number | 目标角色ID |\n| typeId | number | 关系类型ID |\n| description | string | 关系描述 |\n| novelId | number | 所属小说ID |\n| createdAt | string | 创建时间(ISO格式) |\n| sourceCharacter | object | 源角色信息 |\n| targetCharacter | object | 目标角色信息 |\n| relationshipType | object | 关系类型信息 |\n\n**关联数据**:\n- sourceCharacter: 包含角色基本信息和头像\n- targetCharacter: 包含角色基本信息和头像\n- relationshipType: 包含类型名称和颜色"}, {"name": "小说不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationships/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Novel not found\"\n}", "description": "### 小说不存在错误响应\n\n**触发条件**:\n- 提供的小说ID不存在\n- 小说已被删除\n- 用户无权访问该小说\n\n**处理建议**:\n- 检查小说ID是否正确\n- 确认用户权限\n- 刷新小说列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有小说ID", "if (!pm.environment.get('novel_id')) {", "    console.log('警告：未设置novel_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('关系列表响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.be.an('array');", "    });", "    ", "    // 关系数据结构验证", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.test('关系数据结构验证', function () {", "            const relationship = responseJson[0];", "            pm.expect(relationship).to.have.property('id');", "            pm.expect(relationship).to.have.property('sourceId');", "            pm.expect(relationship).to.have.property('targetId');", "            pm.expect(relationship).to.have.property('typeId');", "            pm.expect(relationship).to.have.property('novelId');", "            pm.expect(relationship.novelId).to.equal(parseInt(pm.environment.get('novel_id')));", "        });", "        ", "        // 验证关联数据", "        pm.test('关联数据验证', function () {", "            const relationship = responseJson[0];", "            pm.expect(relationship).to.have.property('sourceCharacter');", "            pm.expect(relationship).to.have.property('targetCharacter');", "            pm.expect(relationship).to.have.property('relationshipType');", "        });", "        ", "        // 保存第一个关系ID用于后续测试", "        pm.environment.set('relationship_id', responseJson[0].id);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证关系属于指定小说", "   - 检查关联数据的完整性", "   - 确认权限控制的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "创建关系", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"sourceId\": {{character_id}},\n  \"targetId\": {{new_character_id}},\n  \"typeId\": {{relationship_type_id}},\n  \"description\": \"这是两个角色之间的重要关系，影响着故事的发展。\",\n  \"novelId\": {{novel_id}}\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| sourceId | number | 是 | 源角色ID | 1 |\n| targetId | number | 是 | 目标角色ID | 2 |\n| typeId | number | 是 | 关系类型ID | 3 |\n| description | string | 否 | 关系描述 | \"两人是好朋友\" |\n| novelId | number | 是 | 所属小说ID | 1 |"}}}, "url": {"raw": "{{base_url}}/api/relationships"}, "description": "#### 创建关系\n\n**功能说明**: 在两个角色之间建立关系，构建角色关系网络\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能在自己的小说中创建关系\n\n**限流规则**: \n- QPS限制：30次/秒\n- 同用户限制：500个关系/小说\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 源角色和目标角色不能相同 | 选择不同的角色 |\n| 400 | 400 | 角色不属于指定小说 | 检查角色和小说的关联 |\n| 400 | 400 | 关系类型无效 | 选择有效的关系类型 |\n| 403 | 403 | 权限不足 | 只能在自己的小说中创建关系 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 源角色和目标角色必须不同\n- 两个角色必须属于同一小说\n- 关系类型必须存在且用户有权使用\n- 同一对角色间可以有多种不同类型的关系\n- 关系具有方向性（源→目标）"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 1,\n  \"targetId\": 2,\n  \"typeId\": 1,\n  \"description\": \"两人是好朋友\",\n  \"novelId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/relationships"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"sourceId\": 1,\n  \"targetId\": 2,\n  \"typeId\": 1,\n  \"description\": \"这是两个角色之间的重要关系，影响着故事的发展。\",\n  \"novelId\": 1,\n  \"createdAt\": \"2024-01-15T17:30:00.000Z\"\n}", "description": "### 创建关系成功响应\n\n**响应字段**: 包含完整的关系信息\n\n**关系方向性**:\n- sourceId → targetId 表示关系的方向\n- 例如：萧炎(1) → 薰儿(2) 表示萧炎对薰儿的关系\n- 如需双向关系，可创建两个相反方向的关系"}, {"name": "角色相同错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 1,\n  \"targetId\": 1,\n  \"typeId\": 1,\n  \"novelId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/relationships"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Source and target characters cannot be the same\"\n}", "description": "### 角色相同错误响应\n\n**触发条件**:\n- 源角色ID和目标角色ID相同\n- 尝试建立角色与自己的关系\n\n**处理建议**:\n- 选择不同的角色作为关系的两端\n- 检查角色选择逻辑\n- 提供角色选择验证"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "// 检查必要的环境变量", "const requiredVars = ['novel_id', 'character_id', 'relationship_type_id'];", "const missingVars = requiredVars.filter(varName => !pm.environment.get(varName));", "", "if (missingVars.length > 0) {", "    console.log(`错误：缺少必要的环境变量: ${missingVars.join(', ')}`);", "    return;", "}", "", "// 如果没有第二个角色ID，使用第一个角色ID+1作为测试", "if (!pm.environment.get('new_character_id')) {", "    const characterId = parseInt(pm.environment.get('character_id'));", "    pm.environment.set('new_character_id', characterId + 1);", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 403]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('sourceId');", "        pm.expect(jsonData).to.have.property('targetId');", "        pm.expect(jsonData).to.have.property('typeId');", "        pm.expect(jsonData).to.have.property('novelId');", "        pm.expect(jsonData.novelId).to.equal(parseInt(pm.environment.get('novel_id')));", "    });", "    ", "    // 验证关系的方向性", "    pm.test('关系方向性验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.sourceId).to.not.equal(jsonData.targetId);", "    });", "    ", "    // 保存新创建的关系ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_relationship_id', responseJson.id);", "}", "", "// 验证错误响应格式", "if (pm.response.code === 400) {", "    pm.test('验证错误响应格式', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证关系的角色关联", "   - 检查关系类型的有效性", "   - 确认权限控制的正确性", "*/"], "type": "text/javascript"}}]}]}}
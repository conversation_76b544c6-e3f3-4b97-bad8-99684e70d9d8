{"module_info": {"name": "书籍信息模块", "description": "外部书籍信息检索、存储和管理", "route_prefix": "/api/books", "dependencies": ["认证管理模块"]}, "folder": {"name": "书籍信息模块", "description": "### 书籍信息模块\n\n#### 业务逻辑\n- 从外部API获取书籍信息\n- 缓存书籍信息到本地数据库\n- 支持书籍信息的搜索和检索\n- 提供书籍信息的CRUD操作\n- 关联小说与外部书籍信息\n\n#### 业务约束\n- 外部ID在系统中唯一\n- 书籍信息支持多种数据源\n- 管理员可管理所有书籍信息\n- 普通用户只能查看和搜索\n\n#### 权限控制\n- 查看和搜索需要登录\n- 创建和更新需要管理员权限\n- 删除操作仅限管理员", "item": [{"name": "获取书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/{{book_id}}", "variable": [{"key": "book_id", "value": "1", "description": "书籍信息ID"}]}, "description": "#### 获取书籍信息\n\n**功能说明**: 根据ID获取特定书籍的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 书籍不存在 | 检查书籍ID是否正确 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 返回完整的书籍信息\n- 包含外部API的原始数据\n- 显示书籍的使用统计"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"externalId\": \"weread_12345\",\n  \"title\": \"斗破苍穹\",\n  \"author\": \"天蚕土豆\",\n  \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n  \"coverImage\": \"https://example.com/cover.jpg\",\n  \"publishedDate\": \"2009-04-14\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 5000000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取书籍信息成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 书籍信息ID |\n| externalId | string | 外部API的唯一标识 |\n| title | string | 书籍标题 |\n| author | string | 作者 |\n| description | string | 书籍描述 |\n| coverImage | string | 封面图片URL |\n| publishedDate | string | 发布日期 |\n| publisher | string | 出版商 |\n| isbn | string | ISBN号码 |\n| pageCount | number | 页数/字数 |\n| categories | array | 分类标签 |\n| language | string | 语言代码 |\n| createdAt | string | 创建时间 |\n| updatedAt | string | 更新时间 |"}, {"name": "书籍不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Book not found\"\n}", "description": "### 书籍不存在错误响应\n\n**触发条件**:\n- 提供的书籍ID不存在\n- 书籍信息已被删除\n\n**处理建议**:\n- 检查书籍ID是否正确\n- 尝试重新搜索书籍"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有书籍ID", "if (!pm.environment.get('book_id')) {", "    console.log('警告：未设置book_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('书籍信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('author');", "    });", "    ", "    // 保存书籍信息用于后续测试", "    const responseJson = pm.response.json();", "    pm.environment.set('book_external_id', responseJson.externalId);", "}", "", "/* 业务逻辑验证：", "   - 验证书籍信息的完整性", "   - 检查外部ID的格式", "   - 确认数据结构的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "通过外部ID获取书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/{{external_id}}", "variable": [{"key": "external_id", "value": "weread_12345", "description": "外部API的书籍ID"}]}, "description": "#### 通过外部ID获取书籍信息\n\n**功能说明**: 根据外部API的ID获取书籍信息，如果本地不存在则从外部API获取\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：50次/秒\n- 外部API调用限制：20次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 外部书籍不存在 | 检查外部ID是否正确 |\n| 503 | 503 | 外部API不可用 | 稍后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 优先返回本地缓存的数据\n- 本地不存在时调用外部API\n- 自动缓存外部API返回的数据\n- 支持多种外部数据源"}, "response": [{"name": "获取成功(本地缓存)", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/weread_12345"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Data-Source", "value": "cache"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"externalId\": \"weread_12345\",\n  \"title\": \"斗破苍穹\",\n  \"author\": \"天蚕土豆\",\n  \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n  \"coverImage\": \"https://example.com/cover.jpg\",\n  \"publishedDate\": \"2009-04-14\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 5000000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取成功响应(本地缓存)\n\n**响应头说明**:\n- X-Data-Source: cache 表示数据来源于本地缓存\n\n**响应字段**: 与获取书籍信息接口相同"}, {"name": "获取成功(外部API)", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/weread_67890"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Data-Source", "value": "external"}], "cookie": [], "body": "{\n  \"id\": 2,\n  \"externalId\": \"weread_67890\",\n  \"title\": \"完美世界\",\n  \"author\": \"辰东\",\n  \"description\": \"一粒尘可填海，一根草斩尽日月星辰，弹指间天翻地覆。\",\n  \"coverImage\": \"https://example.com/cover2.jpg\",\n  \"publishedDate\": \"2013-08-16\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 6800000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T12:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T12:30:00.000Z\"\n}", "description": "### 获取成功响应(外部API)\n\n**响应头说明**:\n- X-Data-Source: external 表示数据来源于外部API\n\n**业务说明**:\n- 数据已自动缓存到本地数据库\n- 下次访问将直接返回缓存数据"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有外部ID", "if (!pm.environment.get('book_external_id')) {", "    console.log('警告：未设置book_external_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 503, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证(外部API调用可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('书籍信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "    });", "    ", "    // 检查数据源", "    const dataSource = pm.response.headers.get('X-Data-Source');", "    if (dataSource) {", "        pm.test('数据源标识验证', function () {", "            pm.expect(['cache', 'external']).to.include(dataSource);", "        });", "        console.log(`数据来源: ${dataSource}`);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证数据源的正确标识", "   - 检查外部API集成的稳定性", "   - 确认缓存机制的有效性", "*/"], "type": "text/javascript"}}]}, {"name": "搜索书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/{{search_query}}?page=1&limit=10", "variable": [{"key": "search_query", "value": "斗破苍穹", "description": "搜索关键词"}], "query": [{"key": "page", "value": "1", "description": "页码，从1开始"}, {"key": "limit", "value": "10", "description": "每页数量，最大50"}]}, "description": "#### 搜索书籍信息\n\n**功能说明**: 根据关键词搜索书籍信息，支持本地搜索和外部API搜索\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：30次/秒\n- 外部搜索限制：10次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 搜索关键词为空 | 提供有效的搜索关键词 |\n| 503 | 503 | 外部搜索服务不可用 | 稍后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 关键词长度至少2个字符\n- 优先搜索本地缓存数据\n- 本地无结果时调用外部API\n- 支持分页查询\n- 自动缓存搜索结果"}, "response": [{"name": "搜索成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/斗破苍穹?page=1&limit=10"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Total-Count", "value": "3"}], "cookie": [], "body": "{\n  \"books\": [\n    {\n      \"id\": 1,\n      \"externalId\": \"weread_12345\",\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n      \"coverImage\": \"https://example.com/cover.jpg\",\n      \"publishedDate\": \"2009-04-14\",\n      \"categories\": [\"玄幻\", \"异世大陆\"]\n    },\n    {\n      \"id\": null,\n      \"externalId\": \"weread_54321\",\n      \"title\": \"斗破苍穹之大主宰\",\n      \"author\": \"天蚕土豆\",\n      \"description\": \"斗破苍穹续作，讲述萧炎之后的故事\",\n      \"coverImage\": \"https://example.com/cover3.jpg\",\n      \"publishedDate\": \"2013-07-12\",\n      \"categories\": [\"玄幻\"]\n    }\n  ],\n  \"pagination\": {\n    \"page\": 1,\n    \"limit\": 10,\n    \"total\": 2,\n    \"totalPages\": 1\n  },\n  \"searchSource\": \"mixed\"\n}", "description": "### 搜索成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| books | array | 书籍信息列表 |\n| books[].id | number/null | 本地书籍ID，null表示仅来自外部 |\n| books[].externalId | string | 外部API的唯一标识 |\n| books[].title | string | 书籍标题 |\n| books[].author | string | 作者 |\n| books[].description | string | 书籍描述 |\n| books[].coverImage | string | 封面图片URL |\n| books[].publishedDate | string | 发布日期 |\n| books[].categories | array | 分类标签 |\n| pagination | object | 分页信息 |\n| pagination.page | number | 当前页码 |\n| pagination.limit | number | 每页数量 |\n| pagination.total | number | 总记录数 |\n| pagination.totalPages | number | 总页数 |\n| searchSource | string | 搜索来源(local/external/mixed) |"}, {"name": "搜索关键词为空", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/?page=1&limit=10"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Search query cannot be empty\"\n}", "description": "### 搜索关键词为空错误响应\n\n**触发条件**:\n- 未提供搜索关键词\n- 搜索关键词长度不足\n\n**处理建议**:\n- 提示用户输入有效的搜索关键词\n- 关键词长度至少2个字符"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 设置默认搜索关键词", "if (!pm.environment.get('search_query')) {", "    pm.environment.set('search_query', '斗破苍穹');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 503, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证(搜索可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(15000);", "});", "", "// 搜索成功时的验证", "if (pm.response.code === 200) {", "    pm.test('搜索结果响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('books');", "        pm.expect(jsonData).to.have.property('pagination');", "        pm.expect(jsonData).to.have.property('searchSource');", "        pm.expect(jsonData.books).to.be.an('array');", "    });", "    ", "    // 验证分页信息", "    pm.test('分页信息验证', function () {", "        const jsonData = pm.response.json();", "        const pagination = jsonData.pagination;", "        pm.expect(pagination).to.have.property('page');", "        pm.expect(pagination).to.have.property('limit');", "        pm.expect(pagination).to.have.property('total');", "        pm.expect(pagination).to.have.property('totalPages');", "    });", "    ", "    // 保存第一个搜索结果", "    const responseJson = pm.response.json();", "    if (responseJson.books.length > 0) {", "        pm.environment.set('search_result_external_id', responseJson.books[0].externalId);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证搜索结果的完整性", "   - 检查分页功能的正确性", "   - 确认搜索来源的标识", "*/"], "type": "text/javascript"}}]}, {"name": "创建书籍信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\",\n  \"coverImage\": \"https://example.com/custom-cover.jpg\",\n  \"publishedDate\": \"2024-01-15\",\n  \"publisher\": \"自定义出版社\",\n  \"isbn\": \"978-0000000000\",\n  \"pageCount\": 300000,\n  \"categories\": [\"原创\", \"都市\"],\n  \"language\": \"zh-CN\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| externalId | string | 是 | 外部唯一标识 | \"custom_001\" |\n| title | string | 是 | 书籍标题 | \"自定义小说\" |\n| author | string | 是 | 作者 | \"作者名\" |\n| description | string | 否 | 书籍描述 | \"这是一本自定义添加的小说\" |\n| coverImage | string | 否 | 封面图片URL | \"https://example.com/cover.jpg\" |\n| publishedDate | string | 否 | 发布日期 | \"2024-01-15\" |\n| publisher | string | 否 | 出版商 | \"自定义出版社\" |\n| isbn | string | 否 | ISBN号码 | \"978-0000000000\" |\n| pageCount | number | 否 | 页数/字数 | 300000 |\n| categories | array | 否 | 分类标签 | [\"原创\", \"都市\"] |\n| language | string | 否 | 语言代码 | \"zh-CN\" |"}}}, "url": {"raw": "{{base_url}}/api/books"}, "description": "#### 创建书籍信息\n\n**功能说明**: 手动创建书籍信息，通常用于添加外部API不支持的书籍\n\n**权限要求**: \n- 角色：管理员\n- 权限：需要管理员权限\n\n**限流规则**: \n- QPS限制：5次/秒\n- 同用户限制：50个/天\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 403 | 403 | 权限不足 | 需要管理员权限 |\n| 400 | 400 | 外部ID已存在 | 更换外部ID |\n| 400 | 400 | 参数验证失败 | 检查必填参数 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 外部ID在系统中必须唯一\n- 标题和作者为必填字段\n- 管理员可以创建任意书籍信息\n- 创建后可被用户搜索和使用"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\"\n}"}, "url": {"raw": "{{base_url}}/api/books"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\",\n  \"coverImage\": \"https://example.com/custom-cover.jpg\",\n  \"publishedDate\": \"2024-01-15\",\n  \"publisher\": \"自定义出版社\",\n  \"isbn\": \"978-0000000000\",\n  \"pageCount\": 300000,\n  \"categories\": [\"原创\", \"都市\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T14:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T14:30:00.000Z\"\n}", "description": "### 创建书籍信息成功响应\n\n**响应字段**: 包含完整的书籍信息，与获取书籍信息接口相同"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态和管理员权限", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "if (pm.environment.get('is_admin') !== 'true') {", "    console.log('错误：需要管理员权限，此请求将失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 403]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('author');", "    });", "    ", "    // 保存新创建的书籍ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_book_id', responseJson.id);", "}", "", "/* 业务逻辑验证：", "   - 验证管理员权限的控制", "   - 检查书籍信息的完整性", "   - 确认外部ID的唯一性", "*/"], "type": "text/javascript"}}]}]}}
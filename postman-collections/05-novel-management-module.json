{"module_info": {"name": "小说管理模块", "description": "小说的完整生命周期管理，包括CRUD操作、文件上传和外部数据集成", "route_prefix": "/api/novels", "dependencies": ["认证管理模块", "小说类型模块", "书籍信息模块"]}, "folder": {"name": "小说管理模块", "description": "### 小说管理模块\n\n#### 业务逻辑\n- 小说的完整CRUD操作\n- 支持封面图片上传和URL设置\n- 从外部书籍信息创建小说\n- 小说状态管理（进行中、已完成、暂停等）\n- 小说与书籍信息的关联管理\n- 级联删除相关角色和关系\n\n#### 业务约束\n- 用户只能管理自己创建的小说\n- 小说标题为必填字段\n- 封面图片支持JPG、PNG、GIF格式，最大10MB\n- 删除小说时会级联删除所有相关数据\n- 支持从外部API创建小说\n\n#### 权限控制\n- 需要登录才能访问所有接口\n- 用户只能操作自己的小说\n- 管理员可以查看和管理所有小说", "item": [{"name": "获取用户所有小说", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels"}, "description": "#### 获取用户所有小说\n\n**功能说明**: 获取当前登录用户创建的所有小说列表\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回当前用户创建的小说\n- 按创建时间倒序排列\n- 包含关联的书籍信息\n- 管理员可以查看所有用户的小说"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"title\": \"我的第一部小说\",\n    \"description\": \"这是一个关于冒险的故事\",\n    \"coverImage\": \"/uploads/abc123.jpg\",\n    \"genre\": \"玄幻\",\n    \"status\": \"In Progress\",\n    \"userId\": 1,\n    \"bookInfoId\": 1,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n    \"updatedAt\": \"2024-01-15T11:00:00.000Z\",\n    \"bookInfo\": {\n      \"id\": 1,\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"coverImage\": \"https://example.com/cover.jpg\"\n    }\n  },\n  {\n    \"id\": 2,\n    \"title\": \"原创小说\",\n    \"description\": \"完全原创的故事情节\",\n    \"coverImage\": \"https://example.com/custom-cover.jpg\",\n    \"genre\": \"都市\",\n    \"status\": \"Completed\",\n    \"userId\": 1,\n    \"bookInfoId\": null,\n    \"createdAt\": \"2024-01-14T15:20:00.000Z\",\n    \"updatedAt\": \"2024-01-15T09:45:00.000Z\",\n    \"bookInfo\": null\n  }\n]", "description": "### 获取小说列表成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 小说唯一标识 |\n| title | string | 小说标题 |\n| description | string | 小说描述 |\n| coverImage | string | 封面图片路径或URL |\n| genre | string | 小说类型 |\n| status | string | 状态(In Progress/Completed/Paused) |\n| userId | number | 创建者ID |\n| bookInfoId | number/null | 关联的书籍信息ID |\n| createdAt | string | 创建时间(ISO格式) |\n| updatedAt | string | 更新时间(ISO格式) |\n| bookInfo | object/null | 关联的书籍信息对象 |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// 小说数据结构验证", "const responseJson = pm.response.json();", "if (responseJson.length > 0) {", "    pm.test('小说数据结构验证', function () {", "        const novel = responseJson[0];", "        pm.expect(novel).to.have.property('id');", "        pm.expect(novel).to.have.property('title');", "        pm.expect(novel).to.have.property('userId');", "        pm.expect(novel).to.have.property('status');", "    });", "    ", "    // 保存第一个小说ID用于后续测试", "    pm.environment.set('novel_id', responseJson[0].id);", "    ", "    // 验证用户权限", "    pm.test('用户权限验证', function () {", "        const currentUserId = parseInt(pm.environment.get('current_user_id'));", "        const isAdmin = pm.environment.get('is_admin') === 'true';", "        ", "        responseJson.forEach(novel => {", "            if (!isAdmin) {", "                pm.expect(novel.userId).to.equal(currentUserId);", "            }", "        });", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证返回的小说属于当前用户", "   - 检查小说数据的完整性", "   - 确认关联数据的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "获取特定小说", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels/{{novel_id}}", "variable": [{"key": "novel_id", "value": "1", "description": "小说ID"}]}, "description": "#### 获取特定小说\n\n**功能说明**: 根据ID获取特定小说的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能访问自己的小说\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 小说不存在 | 检查小说ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的小说 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能查看自己创建的小说\n- 管理员可以查看所有小说\n- 返回完整的小说信息和关联数据"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"title\": \"我的第一部小说\",\n  \"description\": \"这是一个关于冒险的故事，主人公在异世界中不断成长，最终成为传说中的英雄。\",\n  \"coverImage\": \"/uploads/abc123.jpg\",\n  \"genre\": \"玄幻\",\n  \"status\": \"In Progress\",\n  \"userId\": 1,\n  \"bookInfoId\": 1,\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T11:00:00.000Z\",\n  \"bookInfo\": {\n    \"id\": 1,\n    \"externalId\": \"weread_12345\",\n    \"title\": \"斗破苍穹\",\n    \"author\": \"天蚕土豆\",\n    \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n    \"coverImage\": \"https://example.com/cover.jpg\",\n    \"categories\": [\"玄幻\", \"异世大陆\"]\n  }\n}", "description": "### 获取小说详情成功响应\n\n**响应字段**: 与获取小说列表相同，但包含更详细的关联信息\n\n**关联数据**:\n- bookInfo: 完整的书籍信息对象（如果有关联）\n- 包含外部API的原始数据"}, {"name": "小说不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Novel not found\"\n}", "description": "### 小说不存在错误响应\n\n**触发条件**:\n- 提供的小说ID不存在\n- 小说已被删除\n- 用户无权访问该小说\n\n**处理建议**:\n- 检查小说ID是否正确\n- 确认用户权限\n- 刷新小说列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有小说ID", "if (!pm.environment.get('novel_id')) {", "    console.log('警告：未设置novel_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1500);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('小说详情响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('description');", "        pm.expect(jsonData).to.have.property('status');", "        pm.expect(jsonData).to.have.property('userId');", "    });", "    ", "    // 验证用户权限", "    pm.test('用户权限验证', function () {", "        const jsonData = pm.response.json();", "        const currentUserId = parseInt(pm.environment.get('current_user_id'));", "        const isAdmin = pm.environment.get('is_admin') === 'true';", "        ", "        if (!isAdmin) {", "            pm.expect(jsonData.userId).to.equal(currentUserId);", "        }", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证小说信息的完整性", "   - 检查权限控制的正确性", "   - 确认关联数据的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "创建小说", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "description": "支持文件上传的表单类型"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "我的新小说", "description": "小说标题（必填）", "type": "text"}, {"key": "description", "value": "这是一个精彩的故事，讲述了主人公的成长历程。", "description": "小说描述", "type": "text"}, {"key": "genre", "value": "玄幻", "description": "小说类型", "type": "text"}, {"key": "status", "value": "In Progress", "description": "小说状态（In Progress/Completed/Paused）", "type": "text"}, {"key": "coverImage", "description": "封面图片文件（可选）", "type": "file", "src": []}, {"key": "coverImageUrl", "value": "https://example.com/cover.jpg", "description": "封面图片URL（与文件上传二选一）", "type": "text", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/novels"}, "description": "#### 创建小说\n\n**功能说明**: 创建新的小说，支持封面图片上传或URL设置\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同用户限制：50个/天\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 标题不能为空 | 提供小说标题 |\n| 400 | 400 | 文件格式不支持 | 使用JPG/PNG/GIF格式 |\n| 413 | 413 | 文件过大 | 文件大小不超过10MB |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 标题为必填字段\n- 封面图片支持文件上传或URL设置\n- 支持的图片格式：JPG、PNG、GIF\n- 文件大小限制：10MB\n- 自动设置创建者为当前用户"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "我的新小说", "type": "text"}, {"key": "description", "value": "这是一个精彩的故事", "type": "text"}, {"key": "genre", "value": "玄幻", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/novels"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"title\": \"我的新小说\",\n  \"description\": \"这是一个精彩的故事，讲述了主人公的成长历程。\",\n  \"coverImage\": \"/uploads/def456.jpg\",\n  \"genre\": \"玄幻\",\n  \"status\": \"In Progress\",\n  \"userId\": 1,\n  \"bookInfoId\": null,\n  \"createdAt\": \"2024-01-15T14:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T14:30:00.000Z\"\n}", "description": "### 创建小说成功响应\n\n**响应字段**: 包含完整的小说信息\n\n**文件处理**:\n- 上传的文件会保存到 /uploads 目录\n- 文件名会被随机化以避免冲突\n- coverImage 字段包含文件的相对路径"}, {"name": "标题为空错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "description", "value": "没有标题的小说", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/novels"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid novel data\",\n  \"errors\": {\n    \"title\": {\n      \"_errors\": [\"Required\"]\n    }\n  }\n}", "description": "### 数据验证错误响应\n\n**触发条件**:\n- 必填字段缺失\n- 数据格式不正确\n- 字段值超出限制\n\n**处理建议**:\n- 检查所有必填字段\n- 验证数据格式和类型\n- 参考错误详情进行修正"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "// 生成随机小说标题用于测试", "const timestamp = new Date().getTime();", "const randomTitle = `测试小说_${timestamp}`;", "pm.environment.set('random_novel_title', randomTitle);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 413]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData.userId).to.equal(parseInt(pm.environment.get('current_user_id')));", "    });", "    ", "    // 保存新创建的小说ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_novel_id', responseJson.id);", "    ", "    // 验证默认值设置", "    pm.test('默认值验证', function () {", "        const jsonData = pm.response.json();", "        if (!jsonData.status) {", "            pm.expect(jsonData.status).to.equal('In Progress');", "        }", "    });", "}", "", "// 验证错误响应格式", "if (pm.response.code === 400) {", "    pm.test('验证错误响应格式', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证创建者ID的正确设置", "   - 检查文件上传的处理", "   - 确认数据验证的有效性", "*/"], "type": "text/javascript"}}]}]}}
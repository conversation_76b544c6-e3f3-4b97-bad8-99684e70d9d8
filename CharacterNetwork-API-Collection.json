{"info": {"name": "CharacterNetwork API Collection", "description": "## CharacterNetwork API文档\n\n**生成时间**: 2025-06-02 01:53:56\n\n### 快速开始\n1. 设置环境变量 `base_url`\n2. 先运行认证接口\n3. 按模块顺序测试\n\n### 技术栈\n- Express.js + TypeScript\n- PostgreSQL + Drizzle ORM\n- Session认证机制", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "认证管理模块", "description": "### 认证管理模块\n\n#### 业务逻辑\n- 基于Session的用户认证机制\n- 使用Passport.js进行身份验证\n- 支持用户注册、登录、登出操作\n- 提供密码修改和用户信息获取功能\n- 会话数据存储在PostgreSQL中\n\n#### 业务约束\n- 用户名和邮箱必须唯一\n- 密码使用scrypt算法加密存储\n- 会话有效期为7天\n- 登录状态通过Cookie维护\n\n#### 权限控制\n- 部分接口需要用户已登录\n- 管理员权限通过isAdmin字段控制", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| username | string | 是 | 用户名，必须唯一 | \"testuser\" |\n| password | string | 是 | 密码，建议8位以上 | \"password123\" |\n| email | string | 是 | 邮箱地址，必须唯一 | \"<EMAIL>\" |"}}}, "url": {"raw": "{{base_url}}/api/register"}, "description": "#### 用户注册\n\n**功能说明**: 创建新用户账户，完成用户注册流程\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同IP限制：5次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | 用户名已存在 | 更换用户名 |\n| 400 | 400 | 邮箱已存在 | 更换邮箱地址 |\n| 400 | 400 | 参数验证失败 | 检查必填参数格式 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户名长度3-20字符，支持字母数字下划线\n- 密码长度至少6位\n- 邮箱格式必须正确\n- 注册成功后自动登录"}, "response": [{"name": "注册成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/register"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=s%3A...; Path=/; HttpOnly"}], "cookie": [], "body": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"isAdmin\": false,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}", "description": "### 注册成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n| user | object | 用户信息对象 |\n| user.id | number | 用户唯一标识 |\n| user.username | string | 用户名 |\n| user.email | string | 邮箱地址 |\n| user.isAdmin | boolean | 是否为管理员 |\n| user.createdAt | string | 创建时间(ISO格式) |"}, {"name": "用户名已存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"existinguser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/register"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"<PERSON><PERSON><PERSON> already exists\"\n}", "description": "### 用户名冲突错误响应\n\n**触发条件**:\n- 提交的用户名已被其他用户使用\n\n**处理建议**:\n- 提示用户更换用户名\n- 可提供用户名可用性检查功能"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 201, 400]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 成功注册时的验证", "if (pm.response.code === 201) {", "    pm.test('注册成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData).to.have.property('user');", "        pm.expect(jsonData.user).to.have.property('id');", "        pm.expect(jsonData.user).to.have.property('username');", "        pm.expect(jsonData.user).to.have.property('email');", "    });", "    ", "    // 保存用户信息到环境变量", "    const responseJson = pm.response.json();", "    pm.environment.set('user_id', responseJson.user.id);", "    pm.environment.set('username', responseJson.user.username);", "}", "", "/* 业务逻辑验证：", "   - 验证用户信息的完整性", "   - 检查返回的用户ID是否为正整数", "   - 验证isAdmin字段默认为false", "*/"], "type": "text/javascript"}}]}, {"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| username | string | 是 | 用户名或邮箱 | \"testuser\" |\n| password | string | 是 | 用户密码 | \"password123\" |"}}}, "url": {"raw": "{{base_url}}/api/login"}, "description": "#### 用户登录\n\n**功能说明**: 验证用户凭据并建立登录会话\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：20次/秒\n- 同IP限制：10次/分钟\n- 失败限制：5次/5分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 用户名或密码错误 | 检查登录凭据 |\n| 400 | 400 | 参数缺失 | 提供用户名和密码 |\n| 429 | 429 | 登录尝试过于频繁 | 等待后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 支持用户名或邮箱登录\n- 密码错误5次后账户临时锁定\n- 登录成功后创建会话Cookie\n- 会话有效期7天"}, "response": [{"name": "登录成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=s%3A...; Path=/; HttpOnly"}], "cookie": [], "body": "{\n  \"message\": \"Login successful\",\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"isAdmin\": false,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}", "description": "### 登录成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n| user | object | 用户信息对象 |\n| user.id | number | 用户唯一标识 |\n| user.username | string | 用户名 |\n| user.email | string | 邮箱地址 |\n| user.isAdmin | boolean | 是否为管理员 |\n| user.createdAt | string | 创建时间(ISO格式) |"}, {"name": "登录失败", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/api/login"}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid username or password\"\n}", "description": "### 登录失败错误响应\n\n**触发条件**:\n- 用户名不存在\n- 密码错误\n- 账户被锁定\n\n**处理建议**:\n- 提示用户检查登录凭据\n- 提供找回密码功能\n- 显示剩余尝试次数"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 401, 400]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 登录成功时的验证", "if (pm.response.code === 200) {", "    pm.test('登录成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData).to.have.property('user');", "        pm.expect(jsonData.user).to.have.property('id');", "    });", "    ", "    // 保存登录状态到环境变量", "    const responseJson = pm.response.json();", "    pm.environment.set('current_user_id', responseJson.user.id);", "    pm.environment.set('is_logged_in', 'true');", "    pm.environment.set('is_admin', responseJson.user.isAdmin);", "    ", "    // 检查是否设置了会话Cookie", "    pm.test('会话Cookie设置验证', function () {", "        pm.expect(pm.response.headers.get('Set-<PERSON><PERSON>')).to.include('connect.sid');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证会话Cookie的正确设置", "   - 检查用户权限信息", "   - 验证登录状态的持久化", "*/"], "type": "text/javascript"}}]}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n无需请求体参数，通过会话Cookie识别用户身份"}}}, "url": {"raw": "{{base_url}}/api/logout"}, "description": "#### 用户登出\n\n**功能说明**: 销毁用户会话，清除登录状态\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：50次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户未登录或会话已过期 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 清除服务器端会话数据\n- 清除客户端会话Cookie\n- 登出后需要重新登录才能访问受保护资源"}, "response": [{"name": "登出成功", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/logout"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT"}], "cookie": [], "body": "{\n  \"message\": \"Logout successful\"\n}", "description": "### 登出成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n\n**Cookie处理**:\n- 会话Cookie被清除\n- 过期时间设置为过去时间"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 登出成功验证", "pm.test('登出成功响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.message).to.include('successful');", "});", "", "// 清除环境变量中的登录状态", "pm.environment.unset('current_user_id');", "pm.environment.set('is_logged_in', 'false');", "pm.environment.unset('is_admin');", "", "/* 业务逻辑验证：", "   - 验证会话Cookie被正确清除", "   - 确认登录状态被重置", "   - 检查后续请求需要重新认证", "*/"], "type": "text/javascript"}}]}, {"name": "获取当前用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}, "description": "#### 获取当前用户信息\n\n**功能说明**: 获取当前登录用户的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只能获取当前登录用户的信息\n- 不返回敏感信息如密码\n- 包含用户权限信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"isAdmin\": false,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}", "description": "### 获取用户信息成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| user | object | 用户信息对象 |\n| user.id | number | 用户唯一标识 |\n| user.username | string | 用户名 |\n| user.email | string | 邮箱地址 |\n| user.isAdmin | boolean | 是否为管理员 |\n| user.createdAt | string | 创建时间(ISO格式) |"}, {"name": "未登录错误", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Unauthorized\"\n}", "description": "### 未登录错误响应\n\n**触发条件**:\n- 用户未登录\n- 会话已过期\n- 会话Cookie无效\n\n**处理建议**:\n- 重定向到登录页面\n- 提示用户重新登录"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户可能未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 成功获取用户信息时的验证", "if (pm.response.code === 200) {", "    pm.test('用户信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('user');", "        pm.expect(jsonData.user).to.have.property('id');", "        pm.expect(jsonData.user).to.have.property('username');", "        pm.expect(jsonData.user).to.have.property('email');", "        pm.expect(jsonData.user).to.have.property('isAdmin');", "    });", "    ", "    // 验证不包含敏感信息", "    pm.test('敏感信息保护验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.user).to.not.have.property('password');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证用户信息的完整性", "   - 确认敏感信息不被泄露", "   - 检查权限字段的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "修改密码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldpassword123\",\n  \"newPassword\": \"newpassword456\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| currentPassword | string | 是 | 当前密码 | \"oldpassword123\" |\n| newPassword | string | 是 | 新密码，至少6位 | \"newpassword456\" |"}}}, "url": {"raw": "{{base_url}}/api/change-password"}, "description": "#### 修改密码\n\n**功能说明**: 修改当前登录用户的密码\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：5次/秒\n- 同用户限制：3次/小时\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 当前密码错误 | 检查当前密码 |\n| 400 | 400 | 新密码格式不正确 | 密码至少6位 |\n| 429 | 429 | 修改过于频繁 | 等待后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 必须提供正确的当前密码\n- 新密码长度至少6位\n- 新密码不能与当前密码相同\n- 修改成功后不影响当前会话"}, "response": [{"name": "修改成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldpassword123\",\n  \"newPassword\": \"newpassword456\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Password changed successfully\"\n}", "description": "### 密码修改成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n\n**安全说明**:\n- 密码已使用scrypt算法重新加密\n- 当前会话保持有效\n- 建议用户在其他设备重新登录"}, {"name": "当前密码错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"wrongpassword\",\n  \"newPassword\": \"newpassword456\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Current password is incorrect\"\n}", "description": "### 当前密码错误响应\n\n**触发条件**:\n- 提供的当前密码不正确\n\n**处理建议**:\n- 提示用户检查当前密码\n- 提供找回密码功能链接\n- 记录密码错误尝试次数"}, {"name": "新密码格式错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldpassword123\",\n  \"newPassword\": \"123\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"New password must be at least 6 characters long\"\n}", "description": "### 新密码格式错误响应\n\n**触发条件**:\n- 新密码长度不足6位\n- 新密码格式不符合要求\n\n**处理建议**:\n- 显示密码强度要求\n- 提供密码强度检测\n- 建议使用复杂密码"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求将失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 密码修改成功时的验证", "if (pm.response.code === 200) {", "    pm.test('密码修改成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData.message).to.include('successful');", "    });", "    ", "    console.log('密码修改成功，建议在其他设备重新登录');", "}", "", "// 错误响应验证", "if (pm.response.code === 400) {", "    pm.test('错误响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证密码修改的安全性", "   - 确认会话状态保持不变", "   - 检查错误信息的准确性", "*/"], "type": "text/javascript"}}]}]}, {"name": "小说类型模块", "description": "### 小说类型模块\n\n#### 业务逻辑\n- 支持用户创建自定义小说类型\n- 提供系统预设的公共类型\n- 类型可设置为公共或私有\n- 支持类型的描述和分类管理\n- 管理员可管理所有类型\n\n#### 业务约束\n- 用户只能管理自己创建的类型\n- 公共类型对所有用户可见\n- 类型名称在用户范围内唯一\n- 删除类型前需检查是否被小说使用\n\n#### 权限控制\n- 需要登录才能访问\n- 用户只能操作自己的类型\n- 管理员拥有全部权限", "item": [{"name": "获取用户小说类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres"}, "description": "#### 获取用户小说类型\n\n**功能说明**: 获取当前用户的所有小说类型，包括用户自定义类型和公共类型\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 返回用户自定义类型和公共类型\n- 按创建时间倒序排列\n- 包含类型的使用统计信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"玄幻\",\n    \"description\": \"以超自然力量为主题的小说类型\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 2,\n    \"name\": \"都市言情\",\n    \"description\": \"现代都市背景的爱情小说\",\n    \"userId\": 1,\n    \"isPublic\": false,\n    \"createdAt\": \"2024-01-15T11:00:00.000Z\"\n  },\n  {\n    \"id\": 3,\n    \"name\": \"科幻\",\n    \"description\": \"科学幻想类小说\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n]", "description": "### 获取小说类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 类型唯一标识 |\n| name | string | 类型名称 |\n| description | string | 类型描述 |\n| userId | number | 创建者ID，0表示系统类型 |\n| isPublic | boolean | 是否为公共类型 |\n| createdAt | string | 创建时间(ISO格式) |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    ", "    if (jsonData.length > 0) {", "        pm.expect(jsonData[0]).to.have.property('id');", "        pm.expect(jsonData[0]).to.have.property('name');", "        pm.expect(jsonData[0]).to.have.property('userId');", "        pm.expect(jsonData[0]).to.have.property('isPublic');", "    }", "});", "", "// 保存第一个类型ID用于后续测试", "const responseJson = pm.response.json();", "if (responseJson.length > 0) {", "    pm.environment.set('genre_id', responseJson[0].id);", "}", "", "/* 业务逻辑验证：", "   - 验证返回的类型包含公共类型和用户类型", "   - 检查类型数据的完整性", "   - 确认权限字段的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "获取公共小说类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/public"}, "description": "#### 获取公共小说类型\n\n**功能说明**: 获取所有公共小说类型，无需登录即可访问\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回isPublic为true的类型\n- 按名称字母顺序排列\n- 包含系统预设类型"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/public"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"玄幻\",\n    \"description\": \"以超自然力量为主题的小说类型\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 3,\n    \"name\": \"科幻\",\n    \"description\": \"科学幻想类小说\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 5,\n    \"name\": \"武侠\",\n    \"description\": \"中国传统武侠小说\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n]", "description": "### 获取公共类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 类型唯一标识 |\n| name | string | 类型名称 |\n| description | string | 类型描述 |\n| userId | number | 创建者ID，0表示系统类型 |\n| isPublic | boolean | 是否为公共类型(均为true) |\n| createdAt | string | 创建时间(ISO格式) |"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// 验证所有返回的类型都是公共类型", "pm.test('公共类型验证', function () {", "    const jsonData = pm.response.json();", "    jsonData.forEach(genre => {", "        pm.expect(genre.isPublic).to.be.true;", "    });", "});", "", "/* 业务逻辑验证：", "   - 验证只返回公共类型", "   - 检查类型数据的完整性", "   - 确认无需登录即可访问", "*/"], "type": "text/javascript"}}]}, {"name": "获取特定小说类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/{{genre_id}}", "variable": [{"key": "genre_id", "value": "1", "description": "小说类型ID"}]}, "description": "#### 获取特定小说类型\n\n**功能说明**: 根据ID获取特定小说类型的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 类型不存在 | 检查类型ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的或公共类型 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能查看自己创建的类型或公共类型\n- 管理员可以查看所有类型\n- 返回类型的详细信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"name\": \"玄幻\",\n  \"description\": \"以超自然力量为主题的小说类型，包含修仙、魔法、异能等元素\",\n  \"userId\": 0,\n  \"isPublic\": true,\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取类型详情成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 类型唯一标识 |\n| name | string | 类型名称 |\n| description | string | 类型详细描述 |\n| userId | number | 创建者ID，0表示系统类型 |\n| isPublic | boolean | 是否为公共类型 |\n| createdAt | string | 创建时间(ISO格式) |"}, {"name": "类型不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"<PERSON>re not found\"\n}", "description": "### 类型不存在错误响应\n\n**触发条件**:\n- 提供的类型ID不存在\n- 类型已被删除\n\n**处理建议**:\n- 检查类型ID是否正确\n- 刷新类型列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有类型ID", "if (!pm.environment.get('genre_id')) {", "    console.log('警告：未设置genre_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('类型详情响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('description');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData).to.have.property('isPublic');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证类型信息的完整性", "   - 检查权限控制的正确性", "   - 确认数据格式的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "创建小说类型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"悬疑推理\",\n  \"description\": \"以推理解谜为主要情节的小说类型\",\n  \"isPublic\": false\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| name | string | 是 | 类型名称，用户范围内唯一 | \"悬疑推理\" |\n| description | string | 否 | 类型描述 | \"以推理解谜为主要情节的小说类型\" |\n| isPublic | boolean | 否 | 是否为公共类型，默认false | false |"}}}, "url": {"raw": "{{base_url}}/api/genres"}, "description": "#### 创建小说类型\n\n**功能说明**: 创建新的小说类型，可设置为公共或私有\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同用户限制：20个/小时\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 类型名称已存在 | 更换类型名称 |\n| 400 | 400 | 参数验证失败 | 检查必填参数 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 类型名称在用户范围内必须唯一\n- 普通用户创建的类型默认为私有\n- 管理员可以创建公共类型\n- 类型名称长度1-50字符"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"悬疑推理\",\n  \"description\": \"以推理解谜为主要情节的小说类型\",\n  \"isPublic\": false\n}"}, "url": {"raw": "{{base_url}}/api/genres"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"name\": \"悬疑推理\",\n  \"description\": \"以推理解谜为主要情节的小说类型\",\n  \"userId\": 1,\n  \"isPublic\": false,\n  \"createdAt\": \"2024-01-15T12:00:00.000Z\"\n}", "description": "### 创建类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 新创建的类型ID |\n| name | string | 类型名称 |\n| description | string | 类型描述 |\n| userId | number | 创建者ID |\n| isPublic | boolean | 是否为公共类型 |\n| createdAt | string | 创建时间(ISO格式) |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData.userId).to.equal(parseInt(pm.environment.get('current_user_id')));", "    });", "    ", "    // 保存新创建的类型ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_genre_id', responseJson.id);", "}", "", "/* 业务逻辑验证：", "   - 验证创建者ID的正确性", "   - 检查类型属性的设置", "   - 确认权限控制的有效性", "*/"], "type": "text/javascript"}}]}]}, {"name": "书籍信息模块", "description": "### 书籍信息模块\n\n#### 业务逻辑\n- 从外部API获取书籍信息\n- 缓存书籍信息到本地数据库\n- 支持书籍信息的搜索和检索\n- 提供书籍信息的CRUD操作\n- 关联小说与外部书籍信息\n\n#### 业务约束\n- 外部ID在系统中唯一\n- 书籍信息支持多种数据源\n- 管理员可管理所有书籍信息\n- 普通用户只能查看和搜索\n\n#### 权限控制\n- 查看和搜索需要登录\n- 创建和更新需要管理员权限\n- 删除操作仅限管理员", "item": [{"name": "获取书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/{{book_id}}", "variable": [{"key": "book_id", "value": "1", "description": "书籍信息ID"}]}, "description": "#### 获取书籍信息\n\n**功能说明**: 根据ID获取特定书籍的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 书籍不存在 | 检查书籍ID是否正确 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 返回完整的书籍信息\n- 包含外部API的原始数据\n- 显示书籍的使用统计"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"externalId\": \"weread_12345\",\n  \"title\": \"斗破苍穹\",\n  \"author\": \"天蚕土豆\",\n  \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n  \"coverImage\": \"https://example.com/cover.jpg\",\n  \"publishedDate\": \"2009-04-14\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 5000000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取书籍信息成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 书籍信息ID |\n| externalId | string | 外部API的唯一标识 |\n| title | string | 书籍标题 |\n| author | string | 作者 |\n| description | string | 书籍描述 |\n| coverImage | string | 封面图片URL |\n| publishedDate | string | 发布日期 |\n| publisher | string | 出版商 |\n| isbn | string | ISBN号码 |\n| pageCount | number | 页数/字数 |\n| categories | array | 分类标签 |\n| language | string | 语言代码 |\n| createdAt | string | 创建时间 |\n| updatedAt | string | 更新时间 |"}, {"name": "书籍不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Book not found\"\n}", "description": "### 书籍不存在错误响应\n\n**触发条件**:\n- 提供的书籍ID不存在\n- 书籍信息已被删除\n\n**处理建议**:\n- 检查书籍ID是否正确\n- 尝试重新搜索书籍"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有书籍ID", "if (!pm.environment.get('book_id')) {", "    console.log('警告：未设置book_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('书籍信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('author');", "    });", "    ", "    // 保存书籍信息用于后续测试", "    const responseJson = pm.response.json();", "    pm.environment.set('book_external_id', responseJson.externalId);", "}", "", "/* 业务逻辑验证：", "   - 验证书籍信息的完整性", "   - 检查外部ID的格式", "   - 确认数据结构的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "通过外部ID获取书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/{{external_id}}", "variable": [{"key": "external_id", "value": "weread_12345", "description": "外部API的书籍ID"}]}, "description": "#### 通过外部ID获取书籍信息\n\n**功能说明**: 根据外部API的ID获取书籍信息，如果本地不存在则从外部API获取\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：50次/秒\n- 外部API调用限制：20次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 外部书籍不存在 | 检查外部ID是否正确 |\n| 503 | 503 | 外部API不可用 | 稍后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 优先返回本地缓存的数据\n- 本地不存在时调用外部API\n- 自动缓存外部API返回的数据\n- 支持多种外部数据源"}, "response": [{"name": "获取成功(本地缓存)", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/weread_12345"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Data-Source", "value": "cache"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"externalId\": \"weread_12345\",\n  \"title\": \"斗破苍穹\",\n  \"author\": \"天蚕土豆\",\n  \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n  \"coverImage\": \"https://example.com/cover.jpg\",\n  \"publishedDate\": \"2009-04-14\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 5000000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取成功响应(本地缓存)\n\n**响应头说明**:\n- X-Data-Source: cache 表示数据来源于本地缓存\n\n**响应字段**: 与获取书籍信息接口相同"}, {"name": "获取成功(外部API)", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/weread_67890"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Data-Source", "value": "external"}], "cookie": [], "body": "{\n  \"id\": 2,\n  \"externalId\": \"weread_67890\",\n  \"title\": \"完美世界\",\n  \"author\": \"辰东\",\n  \"description\": \"一粒尘可填海，一根草斩尽日月星辰，弹指间天翻地覆。\",\n  \"coverImage\": \"https://example.com/cover2.jpg\",\n  \"publishedDate\": \"2013-08-16\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 6800000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T12:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T12:30:00.000Z\"\n}", "description": "### 获取成功响应(外部API)\n\n**响应头说明**:\n- X-Data-Source: external 表示数据来源于外部API\n\n**业务说明**:\n- 数据已自动缓存到本地数据库\n- 下次访问将直接返回缓存数据"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有外部ID", "if (!pm.environment.get('book_external_id')) {", "    console.log('警告：未设置book_external_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 503, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证(外部API调用可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('书籍信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "    });", "    ", "    // 检查数据源", "    const dataSource = pm.response.headers.get('X-Data-Source');", "    if (dataSource) {", "        pm.test('数据源标识验证', function () {", "            pm.expect(['cache', 'external']).to.include(dataSource);", "        });", "        console.log(`数据来源: ${dataSource}`);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证数据源的正确标识", "   - 检查外部API集成的稳定性", "   - 确认缓存机制的有效性", "*/"], "type": "text/javascript"}}]}, {"name": "搜索书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/{{search_query}}?page=1&limit=10", "variable": [{"key": "search_query", "value": "斗破苍穹", "description": "搜索关键词"}], "query": [{"key": "page", "value": "1", "description": "页码，从1开始"}, {"key": "limit", "value": "10", "description": "每页数量，最大50"}]}, "description": "#### 搜索书籍信息\n\n**功能说明**: 根据关键词搜索书籍信息，支持本地搜索和外部API搜索\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：30次/秒\n- 外部搜索限制：10次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 搜索关键词为空 | 提供有效的搜索关键词 |\n| 503 | 503 | 外部搜索服务不可用 | 稍后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 关键词长度至少2个字符\n- 优先搜索本地缓存数据\n- 本地无结果时调用外部API\n- 支持分页查询\n- 自动缓存搜索结果"}, "response": [{"name": "搜索成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/斗破苍穹?page=1&limit=10"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Total-Count", "value": "3"}], "cookie": [], "body": "{\n  \"books\": [\n    {\n      \"id\": 1,\n      \"externalId\": \"weread_12345\",\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n      \"coverImage\": \"https://example.com/cover.jpg\",\n      \"publishedDate\": \"2009-04-14\",\n      \"categories\": [\"玄幻\", \"异世大陆\"]\n    },\n    {\n      \"id\": null,\n      \"externalId\": \"weread_54321\",\n      \"title\": \"斗破苍穹之大主宰\",\n      \"author\": \"天蚕土豆\",\n      \"description\": \"斗破苍穹续作，讲述萧炎之后的故事\",\n      \"coverImage\": \"https://example.com/cover3.jpg\",\n      \"publishedDate\": \"2013-07-12\",\n      \"categories\": [\"玄幻\"]\n    }\n  ],\n  \"pagination\": {\n    \"page\": 1,\n    \"limit\": 10,\n    \"total\": 2,\n    \"totalPages\": 1\n  },\n  \"searchSource\": \"mixed\"\n}", "description": "### 搜索成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| books | array | 书籍信息列表 |\n| books[].id | number/null | 本地书籍ID，null表示仅来自外部 |\n| books[].externalId | string | 外部API的唯一标识 |\n| books[].title | string | 书籍标题 |\n| books[].author | string | 作者 |\n| books[].description | string | 书籍描述 |\n| books[].coverImage | string | 封面图片URL |\n| books[].publishedDate | string | 发布日期 |\n| books[].categories | array | 分类标签 |\n| pagination | object | 分页信息 |\n| pagination.page | number | 当前页码 |\n| pagination.limit | number | 每页数量 |\n| pagination.total | number | 总记录数 |\n| pagination.totalPages | number | 总页数 |\n| searchSource | string | 搜索来源(local/external/mixed) |"}, {"name": "搜索关键词为空", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/?page=1&limit=10"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Search query cannot be empty\"\n}", "description": "### 搜索关键词为空错误响应\n\n**触发条件**:\n- 未提供搜索关键词\n- 搜索关键词长度不足\n\n**处理建议**:\n- 提示用户输入有效的搜索关键词\n- 关键词长度至少2个字符"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 设置默认搜索关键词", "if (!pm.environment.get('search_query')) {", "    pm.environment.set('search_query', '斗破苍穹');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 503, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证(搜索可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(15000);", "});", "", "// 搜索成功时的验证", "if (pm.response.code === 200) {", "    pm.test('搜索结果响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('books');", "        pm.expect(jsonData).to.have.property('pagination');", "        pm.expect(jsonData).to.have.property('searchSource');", "        pm.expect(jsonData.books).to.be.an('array');", "    });", "    ", "    // 验证分页信息", "    pm.test('分页信息验证', function () {", "        const jsonData = pm.response.json();", "        const pagination = jsonData.pagination;", "        pm.expect(pagination).to.have.property('page');", "        pm.expect(pagination).to.have.property('limit');", "        pm.expect(pagination).to.have.property('total');", "        pm.expect(pagination).to.have.property('totalPages');", "    });", "    ", "    // 保存第一个搜索结果", "    const responseJson = pm.response.json();", "    if (responseJson.books.length > 0) {", "        pm.environment.set('search_result_external_id', responseJson.books[0].externalId);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证搜索结果的完整性", "   - 检查分页功能的正确性", "   - 确认搜索来源的标识", "*/"], "type": "text/javascript"}}]}, {"name": "创建书籍信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\",\n  \"coverImage\": \"https://example.com/custom-cover.jpg\",\n  \"publishedDate\": \"2024-01-15\",\n  \"publisher\": \"自定义出版社\",\n  \"isbn\": \"978-0000000000\",\n  \"pageCount\": 300000,\n  \"categories\": [\"原创\", \"都市\"],\n  \"language\": \"zh-CN\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| externalId | string | 是 | 外部唯一标识 | \"custom_001\" |\n| title | string | 是 | 书籍标题 | \"自定义小说\" |\n| author | string | 是 | 作者 | \"作者名\" |\n| description | string | 否 | 书籍描述 | \"这是一本自定义添加的小说\" |\n| coverImage | string | 否 | 封面图片URL | \"https://example.com/cover.jpg\" |\n| publishedDate | string | 否 | 发布日期 | \"2024-01-15\" |\n| publisher | string | 否 | 出版商 | \"自定义出版社\" |\n| isbn | string | 否 | ISBN号码 | \"978-0000000000\" |\n| pageCount | number | 否 | 页数/字数 | 300000 |\n| categories | array | 否 | 分类标签 | [\"原创\", \"都市\"] |\n| language | string | 否 | 语言代码 | \"zh-CN\" |"}}}, "url": {"raw": "{{base_url}}/api/books"}, "description": "#### 创建书籍信息\n\n**功能说明**: 手动创建书籍信息，通常用于添加外部API不支持的书籍\n\n**权限要求**: \n- 角色：管理员\n- 权限：需要管理员权限\n\n**限流规则**: \n- QPS限制：5次/秒\n- 同用户限制：50个/天\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 403 | 403 | 权限不足 | 需要管理员权限 |\n| 400 | 400 | 外部ID已存在 | 更换外部ID |\n| 400 | 400 | 参数验证失败 | 检查必填参数 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 外部ID在系统中必须唯一\n- 标题和作者为必填字段\n- 管理员可以创建任意书籍信息\n- 创建后可被用户搜索和使用"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\"\n}"}, "url": {"raw": "{{base_url}}/api/books"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\",\n  \"coverImage\": \"https://example.com/custom-cover.jpg\",\n  \"publishedDate\": \"2024-01-15\",\n  \"publisher\": \"自定义出版社\",\n  \"isbn\": \"978-0000000000\",\n  \"pageCount\": 300000,\n  \"categories\": [\"原创\", \"都市\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T14:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T14:30:00.000Z\"\n}", "description": "### 创建书籍信息成功响应\n\n**响应字段**: 包含完整的书籍信息，与获取书籍信息接口相同"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态和管理员权限", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "if (pm.environment.get('is_admin') !== 'true') {", "    console.log('错误：需要管理员权限，此请求将失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 403]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('author');", "    });", "    ", "    // 保存新创建的书籍ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_book_id', responseJson.id);", "}", "", "/* 业务逻辑验证：", "   - 验证管理员权限的控制", "   - 检查书籍信息的完整性", "   - 确认外部ID的唯一性", "*/"], "type": "text/javascript"}}]}]}, {"name": "微信读书代理模块", "description": "### 微信读书代理模块\n\n#### 业务逻辑\n- 代理微信读书API的搜索请求\n- 解决跨域访问问题\n- 提供统一的搜索接口\n- 处理外部API的错误和异常\n- 格式化返回数据结构\n\n#### 业务约束\n- 无需用户登录即可访问\n- 受外部API限流限制\n- 搜索关键词不能为空\n- 依赖外部服务的可用性\n\n#### 权限控制\n- 公开接口，无需认证\n- 基于IP的访问频率限制\n- 防止恶意请求和滥用", "item": [{"name": "微信读书搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword={{search_keyword}}", "query": [{"key": "keyword", "value": "{{search_keyword}}", "description": "搜索关键词"}]}, "description": "#### 微信读书搜索\n\n**功能说明**: 通过微信读书API搜索书籍信息，代理外部请求解决跨域问题\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：20次/秒\n- 同IP限制：100次/小时\n- 外部API限制：依赖微信读书API\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | 搜索关键词不能为空 | 提供有效的搜索关键词 |\n| 503 | 503 | 微信读书API不可用 | 稍后重试 |\n| 500 | 500 | 微信读书API返回非JSON响应 | 检查外部API状态 |\n| 429 | 429 | 请求过于频繁 | 降低请求频率 |\n| 502 | 502 | 外部API错误 | 联系技术支持 |\n\n**业务规则**:\n- 搜索关键词长度至少1个字符\n- 自动处理外部API的响应格式\n- 返回标准化的书籍信息结构\n- 支持中文和英文搜索\n- 实时搜索，不缓存结果"}, "response": [{"name": "搜索成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=斗破苍穹"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Proxy-Source", "value": "weread"}], "cookie": [], "body": "{\n  \"books\": [\n    {\n      \"bookId\": \"12345\",\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"cover\": \"https://weread.qq.com/cover/12345.jpg\",\n      \"intro\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n      \"category\": \"玄幻\",\n      \"publishTime\": \"2009-04-14\",\n      \"publisher\": \"起点中文网\",\n      \"isbn\": \"\",\n      \"totalWords\": 5000000,\n      \"finished\": 1,\n      \"payType\": 0\n    },\n    {\n      \"bookId\": \"67890\",\n      \"title\": \"斗破苍穹之大主宰\",\n      \"author\": \"天蚕土豆\",\n      \"cover\": \"https://weread.qq.com/cover/67890.jpg\",\n      \"intro\": \"斗破苍穹续作，讲述萧炎之后的故事\",\n      \"category\": \"玄幻\",\n      \"publishTime\": \"2013-07-12\",\n      \"publisher\": \"起点中文网\",\n      \"isbn\": \"\",\n      \"totalWords\": 4200000,\n      \"finished\": 1,\n      \"payType\": 1\n    }\n  ],\n  \"totalCount\": 2,\n  \"hasMore\": false\n}", "description": "### 搜索成功响应说明\n\n**响应头说明**:\n- X-Proxy-Source: weread 表示数据来源于微信读书API\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| books | array | 书籍信息列表 |\n| books[].bookId | string | 微信读书的书籍ID |\n| books[].title | string | 书籍标题 |\n| books[].author | string | 作者 |\n| books[].cover | string | 封面图片URL |\n| books[].intro | string | 书籍简介 |\n| books[].category | string | 分类 |\n| books[].publishTime | string | 发布时间 |\n| books[].publisher | string | 出版商 |\n| books[].isbn | string | ISBN号码 |\n| books[].totalWords | number | 总字数 |\n| books[].finished | number | 是否完结(0未完结,1完结) |\n| books[].payType | number | 付费类型(0免费,1付费) |\n| totalCount | number | 搜索结果总数 |\n| hasMore | boolean | 是否有更多结果 |"}, {"name": "搜索关键词为空", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword="}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"搜索关键词不能为空\"\n}", "description": "### 搜索关键词为空错误响应\n\n**触发条件**:\n- 未提供keyword参数\n- keyword参数值为空字符串\n- keyword参数只包含空格\n\n**处理建议**:\n- 提示用户输入有效的搜索关键词\n- 前端进行输入验证\n- 提供搜索建议或热门关键词"}, {"name": "微信读书API不可用", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=测试"}}, "status": "Service Unavailable", "code": 503, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"微信读书API错误: 503\"\n}", "description": "### 外部API不可用错误响应\n\n**触发条件**:\n- 微信读书API服务器故障\n- 网络连接问题\n- 外部API返回错误状态码\n\n**处理建议**:\n- 稍后重试请求\n- 检查网络连接\n- 使用其他数据源作为备选\n- 显示友好的错误提示"}, {"name": "外部API返回非JSON响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=特殊字符"}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"微信读书API返回非JSON响应\",\n  \"contentType\": \"text/html\"\n}", "description": "### 外部API响应格式错误\n\n**触发条件**:\n- 外部API返回HTML页面而非JSON\n- 外部API响应格式异常\n- 搜索关键词包含特殊字符导致错误\n\n**处理建议**:\n- 检查搜索关键词的合法性\n- 联系技术支持检查外部API状态\n- 使用备用搜索方案"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 设置默认搜索关键词", "if (!pm.environment.get('search_keyword')) {", "    pm.environment.set('search_keyword', '斗破苍穹');", "}", "", "// 记录请求开始时间", "pm.environment.set('request_start_time', new Date().getTime());"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 503, 500]).to.include(pm.response.code);", "});", "", "// 响应时间验证(外部API调用可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(15000);", "});", "", "// 搜索成功时的验证", "if (pm.response.code === 200) {", "    pm.test('搜索结果响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('books');", "        pm.expect(jsonData).to.have.property('totalCount');", "        pm.expect(jsonData).to.have.property('hasMore');", "        pm.expect(jsonData.books).to.be.an('array');", "    });", "    ", "    // 验证书籍信息结构", "    const responseJson = pm.response.json();", "    if (responseJson.books.length > 0) {", "        pm.test('书籍信息结构验证', function () {", "            const book = responseJson.books[0];", "            pm.expect(book).to.have.property('bookId');", "            pm.expect(book).to.have.property('title');", "            pm.expect(book).to.have.property('author');", "            pm.expect(book).to.have.property('cover');", "        });", "        ", "        // 保存第一个搜索结果的bookId", "        pm.environment.set('weread_book_id', responseJson.books[0].bookId);", "    }", "    ", "    // 检查代理来源标识", "    const proxySource = pm.response.headers.get('X-Proxy-Source');", "    if (proxySource) {", "        pm.test('代理来源标识验证', function () {", "            pm.expect(proxySource).to.equal('weread');", "        });", "    }", "}", "", "// 错误响应验证", "if (pm.response.code >= 400) {", "    pm.test('错误响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "// 计算实际响应时间", "const startTime = pm.environment.get('request_start_time');", "if (startTime) {", "    const actualResponseTime = new Date().getTime() - parseInt(startTime);", "    console.log(`实际响应时间: ${actualResponseTime}ms`);", "}", "", "/* 业务逻辑验证：", "   - 验证外部API代理的稳定性", "   - 检查数据格式的标准化", "   - 确认错误处理的完整性", "   - 监控外部服务的可用性", "*/"], "type": "text/javascript"}}]}]}, {"name": "小说管理模块", "description": "### 小说管理模块\n\n#### 业务逻辑\n- 小说的完整CRUD操作\n- 支持封面图片上传和URL设置\n- 从外部书籍信息创建小说\n- 小说状态管理（进行中、已完成、暂停等）\n- 小说与书籍信息的关联管理\n- 级联删除相关角色和关系\n\n#### 业务约束\n- 用户只能管理自己创建的小说\n- 小说标题为必填字段\n- 封面图片支持JPG、PNG、GIF格式，最大10MB\n- 删除小说时会级联删除所有相关数据\n- 支持从外部API创建小说\n\n#### 权限控制\n- 需要登录才能访问所有接口\n- 用户只能操作自己的小说\n- 管理员可以查看和管理所有小说", "item": [{"name": "获取用户所有小说", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels"}, "description": "#### 获取用户所有小说\n\n**功能说明**: 获取当前登录用户创建的所有小说列表\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回当前用户创建的小说\n- 按创建时间倒序排列\n- 包含关联的书籍信息\n- 管理员可以查看所有用户的小说"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"title\": \"我的第一部小说\",\n    \"description\": \"这是一个关于冒险的故事\",\n    \"coverImage\": \"/uploads/abc123.jpg\",\n    \"genre\": \"玄幻\",\n    \"status\": \"In Progress\",\n    \"userId\": 1,\n    \"bookInfoId\": 1,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n    \"updatedAt\": \"2024-01-15T11:00:00.000Z\",\n    \"bookInfo\": {\n      \"id\": 1,\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"coverImage\": \"https://example.com/cover.jpg\"\n    }\n  },\n  {\n    \"id\": 2,\n    \"title\": \"原创小说\",\n    \"description\": \"完全原创的故事情节\",\n    \"coverImage\": \"https://example.com/custom-cover.jpg\",\n    \"genre\": \"都市\",\n    \"status\": \"Completed\",\n    \"userId\": 1,\n    \"bookInfoId\": null,\n    \"createdAt\": \"2024-01-14T15:20:00.000Z\",\n    \"updatedAt\": \"2024-01-15T09:45:00.000Z\",\n    \"bookInfo\": null\n  }\n]", "description": "### 获取小说列表成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 小说唯一标识 |\n| title | string | 小说标题 |\n| description | string | 小说描述 |\n| coverImage | string | 封面图片路径或URL |\n| genre | string | 小说类型 |\n| status | string | 状态(In Progress/Completed/Paused) |\n| userId | number | 创建者ID |\n| bookInfoId | number/null | 关联的书籍信息ID |\n| createdAt | string | 创建时间(ISO格式) |\n| updatedAt | string | 更新时间(ISO格式) |\n| bookInfo | object/null | 关联的书籍信息对象 |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// 小说数据结构验证", "const responseJson = pm.response.json();", "if (responseJson.length > 0) {", "    pm.test('小说数据结构验证', function () {", "        const novel = responseJson[0];", "        pm.expect(novel).to.have.property('id');", "        pm.expect(novel).to.have.property('title');", "        pm.expect(novel).to.have.property('userId');", "        pm.expect(novel).to.have.property('status');", "    });", "    ", "    // 保存第一个小说ID用于后续测试", "    pm.environment.set('novel_id', responseJson[0].id);", "    ", "    // 验证用户权限", "    pm.test('用户权限验证', function () {", "        const currentUserId = parseInt(pm.environment.get('current_user_id'));", "        const isAdmin = pm.environment.get('is_admin') === 'true';", "        ", "        responseJson.forEach(novel => {", "            if (!isAdmin) {", "                pm.expect(novel.userId).to.equal(currentUserId);", "            }", "        });", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证返回的小说属于当前用户", "   - 检查小说数据的完整性", "   - 确认关联数据的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "获取特定小说", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels/{{novel_id}}", "variable": [{"key": "novel_id", "value": "1", "description": "小说ID"}]}, "description": "#### 获取特定小说\n\n**功能说明**: 根据ID获取特定小说的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能访问自己的小说\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 小说不存在 | 检查小说ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的小说 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能查看自己创建的小说\n- 管理员可以查看所有小说\n- 返回完整的小说信息和关联数据"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"title\": \"我的第一部小说\",\n  \"description\": \"这是一个关于冒险的故事，主人公在异世界中不断成长，最终成为传说中的英雄。\",\n  \"coverImage\": \"/uploads/abc123.jpg\",\n  \"genre\": \"玄幻\",\n  \"status\": \"In Progress\",\n  \"userId\": 1,\n  \"bookInfoId\": 1,\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T11:00:00.000Z\",\n  \"bookInfo\": {\n    \"id\": 1,\n    \"externalId\": \"weread_12345\",\n    \"title\": \"斗破苍穹\",\n    \"author\": \"天蚕土豆\",\n    \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n    \"coverImage\": \"https://example.com/cover.jpg\",\n    \"categories\": [\"玄幻\", \"异世大陆\"]\n  }\n}", "description": "### 获取小说详情成功响应\n\n**响应字段**: 与获取小说列表相同，但包含更详细的关联信息\n\n**关联数据**:\n- bookInfo: 完整的书籍信息对象（如果有关联）\n- 包含外部API的原始数据"}, {"name": "小说不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Novel not found\"\n}", "description": "### 小说不存在错误响应\n\n**触发条件**:\n- 提供的小说ID不存在\n- 小说已被删除\n- 用户无权访问该小说\n\n**处理建议**:\n- 检查小说ID是否正确\n- 确认用户权限\n- 刷新小说列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有小说ID", "if (!pm.environment.get('novel_id')) {", "    console.log('警告：未设置novel_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1500);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('小说详情响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('description');", "        pm.expect(jsonData).to.have.property('status');", "        pm.expect(jsonData).to.have.property('userId');", "    });", "    ", "    // 验证用户权限", "    pm.test('用户权限验证', function () {", "        const jsonData = pm.response.json();", "        const currentUserId = parseInt(pm.environment.get('current_user_id'));", "        const isAdmin = pm.environment.get('is_admin') === 'true';", "        ", "        if (!isAdmin) {", "            pm.expect(jsonData.userId).to.equal(currentUserId);", "        }", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证小说信息的完整性", "   - 检查权限控制的正确性", "   - 确认关联数据的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "创建小说", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "description": "支持文件上传的表单类型"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "我的新小说", "description": "小说标题（必填）", "type": "text"}, {"key": "description", "value": "这是一个精彩的故事，讲述了主人公的成长历程。", "description": "小说描述", "type": "text"}, {"key": "genre", "value": "玄幻", "description": "小说类型", "type": "text"}, {"key": "status", "value": "In Progress", "description": "小说状态（In Progress/Completed/Paused）", "type": "text"}, {"key": "coverImage", "description": "封面图片文件（可选）", "type": "file", "src": []}, {"key": "coverImageUrl", "value": "https://example.com/cover.jpg", "description": "封面图片URL（与文件上传二选一）", "type": "text", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/novels"}, "description": "#### 创建小说\n\n**功能说明**: 创建新的小说，支持封面图片上传或URL设置\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同用户限制：50个/天\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 标题不能为空 | 提供小说标题 |\n| 400 | 400 | 文件格式不支持 | 使用JPG/PNG/GIF格式 |\n| 413 | 413 | 文件过大 | 文件大小不超过10MB |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 标题为必填字段\n- 封面图片支持文件上传或URL设置\n- 支持的图片格式：JPG、PNG、GIF\n- 文件大小限制：10MB\n- 自动设置创建者为当前用户"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "我的新小说", "type": "text"}, {"key": "description", "value": "这是一个精彩的故事", "type": "text"}, {"key": "genre", "value": "玄幻", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/novels"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"title\": \"我的新小说\",\n  \"description\": \"这是一个精彩的故事，讲述了主人公的成长历程。\",\n  \"coverImage\": \"/uploads/def456.jpg\",\n  \"genre\": \"玄幻\",\n  \"status\": \"In Progress\",\n  \"userId\": 1,\n  \"bookInfoId\": null,\n  \"createdAt\": \"2024-01-15T14:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T14:30:00.000Z\"\n}", "description": "### 创建小说成功响应\n\n**响应字段**: 包含完整的小说信息\n\n**文件处理**:\n- 上传的文件会保存到 /uploads 目录\n- 文件名会被随机化以避免冲突\n- coverImage 字段包含文件的相对路径"}, {"name": "标题为空错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "description", "value": "没有标题的小说", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/novels"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid novel data\",\n  \"errors\": {\n    \"title\": {\n      \"_errors\": [\"Required\"]\n    }\n  }\n}", "description": "### 数据验证错误响应\n\n**触发条件**:\n- 必填字段缺失\n- 数据格式不正确\n- 字段值超出限制\n\n**处理建议**:\n- 检查所有必填字段\n- 验证数据格式和类型\n- 参考错误详情进行修正"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "// 生成随机小说标题用于测试", "const timestamp = new Date().getTime();", "const randomTitle = `测试小说_${timestamp}`;", "pm.environment.set('random_novel_title', randomTitle);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 413]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData.userId).to.equal(parseInt(pm.environment.get('current_user_id')));", "    });", "    ", "    // 保存新创建的小说ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_novel_id', responseJson.id);", "    ", "    // 验证默认值设置", "    pm.test('默认值验证', function () {", "        const jsonData = pm.response.json();", "        if (!jsonData.status) {", "            pm.expect(jsonData.status).to.equal('In Progress');", "        }", "    });", "}", "", "// 验证错误响应格式", "if (pm.response.code === 400) {", "    pm.test('验证错误响应格式', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证创建者ID的正确设置", "   - 检查文件上传的处理", "   - 确认数据验证的有效性", "*/"], "type": "text/javascript"}}]}]}, {"name": "角色管理模块", "description": "### 角色管理模块\n\n#### 业务逻辑\n- 角色的完整CRUD操作\n- 支持头像图片上传和URL设置\n- 角色与小说的关联管理\n- 角色描述和属性信息维护\n- 角色在关系网络中的节点管理\n\n#### 业务约束\n- 角色必须属于特定小说\n- 用户只能管理自己小说中的角色\n- 角色名称在同一小说中建议唯一\n- 头像图片支持JPG、PNG、GIF格式，最大10MB\n- 删除角色时会同时删除相关关系\n\n#### 权限控制\n- 需要登录才能访问所有接口\n- 用户只能操作自己小说中的角色\n- 管理员可以查看和管理所有角色", "item": [{"name": "获取小说的所有角色", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/{{novel_id}}", "variable": [{"key": "novel_id", "value": "1", "description": "小说ID"}]}, "description": "#### 获取小说的所有角色\n\n**功能说明**: 获取指定小说中的所有角色列表\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能访问自己小说的角色\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 小说不存在 | 检查小说ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的小说 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回指定小说中的角色\n- 按创建时间倒序排列\n- 包含角色的基本信息和头像\n- 用户只能查看自己小说的角色"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"萧炎\",\n    \"description\": \"主人公，天才少年，拥有强大的斗气天赋\",\n    \"avatar\": \"/uploads/character_abc123.jpg\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 2,\n    \"name\": \"薰儿\",\n    \"description\": \"女主角，古族小姐，萧炎的青梅竹马\",\n    \"avatar\": \"https://example.com/xuner-avatar.jpg\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T11:00:00.000Z\"\n  },\n  {\n    \"id\": 3,\n    \"name\": \"药老\",\n    \"description\": \"萧炎的师父，炼药师，灵魂状态\",\n    \"avatar\": null,\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T11:30:00.000Z\"\n  }\n]", "description": "### 获取角色列表成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 角色唯一标识 |\n| name | string | 角色名称 |\n| description | string | 角色描述 |\n| avatar | string/null | 头像图片路径或URL |\n| novelId | number | 所属小说ID |\n| createdAt | string | 创建时间(ISO格式) |"}, {"name": "小说不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Novel not found\"\n}", "description": "### 小说不存在错误响应\n\n**触发条件**:\n- 提供的小说ID不存在\n- 小说已被删除\n- 用户无权访问该小说\n\n**处理建议**:\n- 检查小说ID是否正确\n- 确认用户权限\n- 刷新小说列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有小说ID", "if (!pm.environment.get('novel_id')) {", "    console.log('警告：未设置novel_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('角色列表响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.be.an('array');", "    });", "    ", "    // 角色数据结构验证", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.test('角色数据结构验证', function () {", "            const character = responseJson[0];", "            pm.expect(character).to.have.property('id');", "            pm.expect(character).to.have.property('name');", "            pm.expect(character).to.have.property('novelId');", "            pm.expect(character.novelId).to.equal(parseInt(pm.environment.get('novel_id')));", "        });", "        ", "        // 保存第一个角色ID用于后续测试", "        pm.environment.set('character_id', responseJson[0].id);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证角色属于指定小说", "   - 检查角色数据的完整性", "   - 确认权限控制的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "获取特定角色", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/single/{{character_id}}", "variable": [{"key": "character_id", "value": "1", "description": "角色ID"}]}, "description": "#### 获取特定角色\n\n**功能说明**: 根据ID获取特定角色的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能访问自己小说的角色\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 角色不存在 | 检查角色ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己小说的角色 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能查看自己小说中的角色\n- 管理员可以查看所有角色\n- 返回完整的角色信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/single/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"name\": \"萧炎\",\n  \"description\": \"主人公，天才少年，拥有强大的斗气天赋。从乌坦城的废物少年成长为斗帝，经历了无数磨难和挑战。\",\n  \"avatar\": \"/uploads/character_abc123.jpg\",\n  \"novelId\": 1,\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取角色详情成功响应\n\n**响应字段**: 与角色列表相同，但包含更详细的描述信息"}, {"name": "角色不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/single/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Character not found\"\n}", "description": "### 角色不存在错误响应\n\n**触发条件**:\n- 提供的角色ID不存在\n- 角色已被删除\n- 用户无权访问该角色\n\n**处理建议**:\n- 检查角色ID是否正确\n- 确认用户权限\n- 刷新角色列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有角色ID", "if (!pm.environment.get('character_id')) {", "    console.log('警告：未设置character_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1500);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('角色详情响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('description');", "        pm.expect(jsonData).to.have.property('novelId');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证角色信息的完整性", "   - 检查权限控制的正确性", "   - 确认数据格式的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "创建角色", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "description": "支持文件上传的表单类型"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "新角色", "description": "角色名称（必填）", "type": "text"}, {"key": "description", "value": "这是一个重要的角色，在故事中扮演关键作用。", "description": "角色描述", "type": "text"}, {"key": "novelId", "value": "{{novel_id}}", "description": "所属小说ID（必填）", "type": "text"}, {"key": "avatar", "description": "头像图片文件（可选）", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/characters"}, "description": "#### 创建角色\n\n**功能说明**: 在指定小说中创建新角色，支持头像图片上传\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能在自己的小说中创建角色\n\n**限流规则**: \n- QPS限制：20次/秒\n- 同用户限制：100个角色/小说\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 角色名称不能为空 | 提供角色名称 |\n| 400 | 400 | 小说ID无效 | 检查小说ID |\n| 403 | 403 | 权限不足 | 只能在自己的小说中创建角色 |\n| 413 | 413 | 文件过大 | 头像文件不超过10MB |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 角色名称为必填字段\n- 必须指定有效的小说ID\n- 头像图片支持JPG、PNG、GIF格式\n- 文件大小限制：10MB\n- 角色名称在同一小说中建议唯一"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "新角色", "type": "text"}, {"key": "description", "value": "这是一个重要的角色", "type": "text"}, {"key": "novelId", "value": "1", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/characters"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"name\": \"新角色\",\n  \"description\": \"这是一个重要的角色，在故事中扮演关键作用。\",\n  \"avatar\": \"/uploads/character_def456.jpg\",\n  \"novelId\": 1,\n  \"createdAt\": \"2024-01-15T15:30:00.000Z\"\n}", "description": "### 创建角色成功响应\n\n**响应字段**: 包含完整的角色信息\n\n**文件处理**:\n- 上传的头像会保存到 /uploads 目录\n- 文件名会被随机化以避免冲突\n- avatar 字段包含文件的相对路径"}, {"name": "角色名称为空错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "description", "value": "没有名称的角色", "type": "text"}, {"key": "novelId", "value": "1", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/characters"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid character data\",\n  \"errors\": {\n    \"name\": {\n      \"_errors\": [\"Required\"]\n    }\n  }\n}", "description": "### 数据验证错误响应\n\n**触发条件**:\n- 必填字段缺失\n- 数据格式不正确\n- 小说ID无效\n\n**处理建议**:\n- 检查所有必填字段\n- 验证小说ID的有效性\n- 确认用户对小说的权限"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "// 检查小说ID", "if (!pm.environment.get('novel_id')) {", "    console.log('错误：未设置novel_id环境变量');", "    return;", "}", "", "// 生成随机角色名称用于测试", "const timestamp = new Date().getTime();", "const randomName = `测试角色_${timestamp}`;", "pm.environment.set('random_character_name', randomName);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 403, 413]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('novelId');", "        pm.expect(jsonData.novelId).to.equal(parseInt(pm.environment.get('novel_id')));", "    });", "    ", "    // 保存新创建的角色ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_character_id', responseJson.id);", "}", "", "// 验证错误响应格式", "if (pm.response.code === 400) {", "    pm.test('验证错误响应格式', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证角色与小说的关联", "   - 检查文件上传的处理", "   - 确认数据验证的有效性", "*/"], "type": "text/javascript"}}]}]}, {"name": "关系类型模块", "description": "### 关系类型模块\n\n#### 业务逻辑\n- 关系类型的完整CRUD操作\n- 支持用户自定义关系类型\n- 提供系统预设的默认关系类型\n- 关系类型的颜色和样式管理\n- 关系类型在关系图谱中的视觉表现\n\n#### 业务约束\n- 用户只能管理自己创建的关系类型\n- 系统默认类型对所有用户可见\n- 关系类型名称在用户范围内唯一\n- 颜色值使用十六进制格式\n- 删除类型前需检查是否被关系使用\n\n#### 权限控制\n- 需要登录才能访问所有接口\n- 用户只能操作自己的关系类型\n- 管理员可以管理系统默认类型", "item": [{"name": "获取用户关系类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types"}, "description": "#### 获取用户关系类型\n\n**功能说明**: 获取当前用户的所有关系类型，包括用户自定义类型和系统默认类型\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 返回用户自定义类型和系统默认类型\n- 按创建时间倒序排列\n- 包含类型的颜色和样式信息\n- 显示类型的使用统计"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"朋友\",\n    \"color\": \"#4CAF50\",\n    \"userId\": 0,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 2,\n    \"name\": \"敌人\",\n    \"color\": \"#F44336\",\n    \"userId\": 0,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 3,\n    \"name\": \"恋人\",\n    \"color\": \"#E91E63\",\n    \"userId\": 0,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 10,\n    \"name\": \"师徒\",\n    \"color\": \"#9C27B0\",\n    \"userId\": 1,\n    \"createdAt\": \"2024-01-15T14:00:00.000Z\"\n  },\n  {\n    \"id\": 11,\n    \"name\": \"竞争对手\",\n    \"color\": \"#FF9800\",\n    \"userId\": 1,\n    \"createdAt\": \"2024-01-15T15:00:00.000Z\"\n  }\n]", "description": "### 获取关系类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 关系类型唯一标识 |\n| name | string | 关系类型名称 |\n| color | string | 颜色值(十六进制格式) |\n| userId | number | 创建者ID，0表示系统默认 |\n| createdAt | string | 创建时间(ISO格式) |\n\n**系统默认类型**:\n- userId为0的类型是系统预设的\n- 所有用户都可以使用系统默认类型\n- 系统默认类型不能被普通用户修改或删除"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// 关系类型数据结构验证", "const responseJson = pm.response.json();", "if (responseJson.length > 0) {", "    pm.test('关系类型数据结构验证', function () {", "        const type = responseJson[0];", "        pm.expect(type).to.have.property('id');", "        pm.expect(type).to.have.property('name');", "        pm.expect(type).to.have.property('color');", "        pm.expect(type).to.have.property('userId');", "    });", "    ", "    // 保存第一个关系类型ID用于后续测试", "    pm.environment.set('relationship_type_id', responseJson[0].id);", "    ", "    // 验证颜色格式", "    pm.test('颜色格式验证', function () {", "        responseJson.forEach(type => {", "            pm.expect(type.color).to.match(/^#[0-9A-Fa-f]{6}$/);", "        });", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证系统默认类型和用户类型的区分", "   - 检查颜色值的格式正确性", "   - 确认数据的完整性", "*/"], "type": "text/javascript"}}]}, {"name": "获取特定关系类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types/{{relationship_type_id}}", "variable": [{"key": "relationship_type_id", "value": "1", "description": "关系类型ID"}]}, "description": "#### 获取特定关系类型\n\n**功能说明**: 根据ID获取特定关系类型的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 关系类型不存在 | 检查类型ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的或系统默认类型 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能查看自己创建的类型或系统默认类型\n- 管理员可以查看所有类型\n- 返回类型的详细信息和使用统计"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"name\": \"朋友\",\n  \"color\": \"#4CAF50\",\n  \"userId\": 0,\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取关系类型详情成功响应\n\n**响应字段**: 与获取关系类型列表相同\n\n**系统默认类型**:\n- 朋友 (#4CAF50 - 绿色)\n- 敌人 (#F44336 - 红色)\n- 恋人 (#E91E63 - 粉色)\n- 亲人 (#2196F3 - 蓝色)\n- 师徒 (#9C27B0 - 紫色)"}, {"name": "关系类型不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Relationship type not found\"\n}", "description": "### 关系类型不存在错误响应\n\n**触发条件**:\n- 提供的类型ID不存在\n- 类型已被删除\n- 用户无权访问该类型\n\n**处理建议**:\n- 检查类型ID是否正确\n- 确认用户权限\n- 刷新类型列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有关系类型ID", "if (!pm.environment.get('relationship_type_id')) {", "    console.log('警告：未设置relationship_type_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('关系类型详情响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('color');", "        pm.expect(jsonData).to.have.property('userId');", "    });", "    ", "    // 验证颜色格式", "    pm.test('颜色格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.color).to.match(/^#[0-9A-Fa-f]{6}$/);", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证关系类型信息的完整性", "   - 检查权限控制的正确性", "   - 确认颜色值的有效性", "*/"], "type": "text/javascript"}}]}, {"name": "创建关系类型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"盟友\",\n  \"color\": \"#00BCD4\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| name | string | 是 | 关系类型名称，用户范围内唯一 | \"盟友\" |\n| color | string | 是 | 颜色值，十六进制格式 | \"#00BCD4\" |"}}}, "url": {"raw": "{{base_url}}/api/relationship-types"}, "description": "#### 创建关系类型\n\n**功能说明**: 创建新的关系类型，用于定义角色间关系的分类\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同用户限制：50个/天\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 类型名称已存在 | 更换类型名称 |\n| 400 | 400 | 颜色格式无效 | 使用正确的十六进制格式 |\n| 400 | 400 | 参数验证失败 | 检查必填参数 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 类型名称在用户范围内必须唯一\n- 颜色值必须是有效的十六进制格式(#RRGGBB)\n- 自动设置创建者为当前用户\n- 类型名称长度1-20字符\n- 建议使用有意义的颜色来区分不同关系"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"盟友\",\n  \"color\": \"#00BCD4\"\n}"}, "url": {"raw": "{{base_url}}/api/relationship-types"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 20,\n  \"name\": \"盟友\",\n  \"color\": \"#00BCD4\",\n  \"userId\": 1,\n  \"createdAt\": \"2024-01-15T16:30:00.000Z\"\n}", "description": "### 创建关系类型成功响应\n\n**响应字段**: 包含完整的关系类型信息\n\n**颜色建议**:\n- 正面关系：绿色系 (#4CAF50, #8BC34A)\n- 负面关系：红色系 (#F44336, #FF5722)\n- 中性关系：蓝色系 (#2196F3, #00BCD4)\n- 特殊关系：紫色系 (#9C27B0, #673AB7)"}, {"name": "类型名称已存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"朋友\",\n  \"color\": \"#4CAF50\"\n}"}, "url": {"raw": "{{base_url}}/api/relationship-types"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Relationship type name already exists\"\n}", "description": "### 类型名称冲突错误响应\n\n**触发条件**:\n- 用户已创建同名的关系类型\n- 与系统默认类型名称冲突\n\n**处理建议**:\n- 提示用户更换类型名称\n- 显示已存在的类型列表\n- 建议使用更具体的描述"}, {"name": "颜色格式无效", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"新类型\",\n  \"color\": \"red\"\n}"}, "url": {"raw": "{{base_url}}/api/relationship-types"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid color format. Use hex format like #FF0000\"\n}", "description": "### 颜色格式错误响应\n\n**触发条件**:\n- 颜色值不是十六进制格式\n- 缺少#前缀\n- 长度不是7位字符\n\n**处理建议**:\n- 使用颜色选择器组件\n- 验证颜色格式：#RRGGBB\n- 提供常用颜色预设"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "// 生成随机关系类型名称用于测试", "const timestamp = new Date().getTime();", "const randomName = `测试关系_${timestamp}`;", "pm.environment.set('random_relationship_type_name', randomName);", "", "// 生成随机颜色", "const colors = ['#FF5722', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5', '#2196F3', '#00BCD4', '#009688', '#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800'];", "const randomColor = colors[Math.floor(Math.random() * colors.length)];", "pm.environment.set('random_color', randomColor);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('color');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData.userId).to.equal(parseInt(pm.environment.get('current_user_id')));", "    });", "    ", "    // 验证颜色格式", "    pm.test('颜色格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.color).to.match(/^#[0-9A-Fa-f]{6}$/);", "    });", "    ", "    // 保存新创建的关系类型ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_relationship_type_id', responseJson.id);", "}", "", "// 验证错误响应格式", "if (pm.response.code === 400) {", "    pm.test('验证错误响应格式', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证创建者ID的正确设置", "   - 检查颜色值的格式验证", "   - 确认名称唯一性检查", "*/"], "type": "text/javascript"}}]}, {"name": "删除关系类型", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types/{{new_relationship_type_id}}", "variable": [{"key": "new_relationship_type_id", "value": "20", "description": "要删除的关系类型ID"}]}, "description": "#### 删除关系类型\n\n**功能说明**: 删除用户创建的关系类型\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能删除自己创建的类型\n\n**限流规则**: \n- QPS限制：10次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 关系类型不存在 | 检查类型ID是否正确 |\n| 403 | 403 | 权限不足 | 只能删除自己创建的类型 |\n| 400 | 400 | 类型正在使用中 | 先删除使用该类型的关系 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能删除自己创建的关系类型\n- 系统默认类型不能被删除\n- 删除前检查是否有关系正在使用该类型\n- 管理员可以删除任何用户创建的类型"}, "response": [{"name": "删除成功", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types/20"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Relationship type deleted successfully\"\n}", "description": "### 删除关系类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n\n**后续影响**:\n- 该类型不再出现在用户的类型列表中\n- 使用该类型的关系需要重新分配类型"}, {"name": "类型正在使用中", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/relationship-types/10"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Cannot delete relationship type that is in use\",\n  \"usageCount\": 3\n}", "description": "### 类型使用中错误响应\n\n**触发条件**:\n- 有关系正在使用该类型\n- 类型被其他数据引用\n\n**处理建议**:\n- 先删除或修改使用该类型的关系\n- 提供批量修改关系类型的功能\n- 显示使用该类型的关系列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有关系类型ID", "if (!pm.environment.get('new_relationship_type_id')) {", "    console.log('警告：未设置new_relationship_type_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 401, 403, 404]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 删除成功时的验证", "if (pm.response.code === 200) {", "    pm.test('删除成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData.message).to.include('successful');", "    });", "}", "", "// 使用中错误的验证", "if (pm.response.code === 400) {", "    pm.test('使用中错误响应验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        if (jsonData.usageCount !== undefined) {", "            pm.expect(jsonData.usageCount).to.be.a('number');", "        }", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证权限控制的正确性", "   - 检查使用状态的检查", "   - 确认删除操作的安全性", "*/"], "type": "text/javascript"}}]}]}, {"name": "关系管理模块", "description": "### 关系管理模块\n\n#### 业务逻辑\n- 角色间关系的完整CRUD操作\n- 支持有向和无向关系定义\n- 关系类型的分类和可视化\n- 关系网络的构建和分析\n- 关系描述和详细信息管理\n- 关系在图谱中的展示和交互\n\n#### 业务约束\n- 关系必须在同一小说的角色间建立\n- 用户只能管理自己小说中的关系\n- 关系的源角色和目标角色不能相同\n- 必须指定有效的关系类型\n- 同一对角色间可以有多种不同类型的关系\n\n#### 权限控制\n- 需要登录才能访问所有接口\n- 用户只能操作自己小说中的关系\n- 管理员可以查看和管理所有关系", "item": [{"name": "获取小说的所有关系", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationships/{{novel_id}}", "variable": [{"key": "novel_id", "value": "1", "description": "小说ID"}]}, "description": "#### 获取小说的所有关系\n\n**功能说明**: 获取指定小说中所有角色间的关系列表\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能访问自己小说的关系\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 小说不存在 | 检查小说ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的小说 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回指定小说中的关系\n- 包含关系的详细信息和关联数据\n- 按创建时间倒序排列\n- 用户只能查看自己小说的关系"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationships/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"sourceId\": 1,\n    \"targetId\": 2,\n    \"typeId\": 3,\n    \"description\": \"萧炎和薰儿从小一起长大，是青梅竹马的关系\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T12:00:00.000Z\",\n    \"sourceCharacter\": {\n      \"id\": 1,\n      \"name\": \"萧炎\",\n      \"avatar\": \"/uploads/character_abc123.jpg\"\n    },\n    \"targetCharacter\": {\n      \"id\": 2,\n      \"name\": \"薰儿\",\n      \"avatar\": \"https://example.com/xuner-avatar.jpg\"\n    },\n    \"relationshipType\": {\n      \"id\": 3,\n      \"name\": \"恋人\",\n      \"color\": \"#E91E63\"\n    }\n  },\n  {\n    \"id\": 2,\n    \"sourceId\": 1,\n    \"targetId\": 3,\n    \"typeId\": 10,\n    \"description\": \"药老是萧炎的师父，传授他炼药术和斗技\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-01-15T13:00:00.000Z\",\n    \"sourceCharacter\": {\n      \"id\": 1,\n      \"name\": \"萧炎\",\n      \"avatar\": \"/uploads/character_abc123.jpg\"\n    },\n    \"targetCharacter\": {\n      \"id\": 3,\n      \"name\": \"药老\",\n      \"avatar\": null\n    },\n    \"relationshipType\": {\n      \"id\": 10,\n      \"name\": \"师徒\",\n      \"color\": \"#9C27B0\"\n    }\n  }\n]", "description": "### 获取关系列表成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 关系唯一标识 |\n| sourceId | number | 源角色ID |\n| targetId | number | 目标角色ID |\n| typeId | number | 关系类型ID |\n| description | string | 关系描述 |\n| novelId | number | 所属小说ID |\n| createdAt | string | 创建时间(ISO格式) |\n| sourceCharacter | object | 源角色信息 |\n| targetCharacter | object | 目标角色信息 |\n| relationshipType | object | 关系类型信息 |\n\n**关联数据**:\n- sourceCharacter: 包含角色基本信息和头像\n- targetCharacter: 包含角色基本信息和头像\n- relationshipType: 包含类型名称和颜色"}, {"name": "小说不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/relationships/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Novel not found\"\n}", "description": "### 小说不存在错误响应\n\n**触发条件**:\n- 提供的小说ID不存在\n- 小说已被删除\n- 用户无权访问该小说\n\n**处理建议**:\n- 检查小说ID是否正确\n- 确认用户权限\n- 刷新小说列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有小说ID", "if (!pm.environment.get('novel_id')) {", "    console.log('警告：未设置novel_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('关系列表响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.be.an('array');", "    });", "    ", "    // 关系数据结构验证", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.test('关系数据结构验证', function () {", "            const relationship = responseJson[0];", "            pm.expect(relationship).to.have.property('id');", "            pm.expect(relationship).to.have.property('sourceId');", "            pm.expect(relationship).to.have.property('targetId');", "            pm.expect(relationship).to.have.property('typeId');", "            pm.expect(relationship).to.have.property('novelId');", "            pm.expect(relationship.novelId).to.equal(parseInt(pm.environment.get('novel_id')));", "        });", "        ", "        // 验证关联数据", "        pm.test('关联数据验证', function () {", "            const relationship = responseJson[0];", "            pm.expect(relationship).to.have.property('sourceCharacter');", "            pm.expect(relationship).to.have.property('targetCharacter');", "            pm.expect(relationship).to.have.property('relationshipType');", "        });", "        ", "        // 保存第一个关系ID用于后续测试", "        pm.environment.set('relationship_id', responseJson[0].id);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证关系属于指定小说", "   - 检查关联数据的完整性", "   - 确认权限控制的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "创建关系", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"sourceId\": {{character_id}},\n  \"targetId\": {{new_character_id}},\n  \"typeId\": {{relationship_type_id}},\n  \"description\": \"这是两个角色之间的重要关系，影响着故事的发展。\",\n  \"novelId\": {{novel_id}}\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| sourceId | number | 是 | 源角色ID | 1 |\n| targetId | number | 是 | 目标角色ID | 2 |\n| typeId | number | 是 | 关系类型ID | 3 |\n| description | string | 否 | 关系描述 | \"两人是好朋友\" |\n| novelId | number | 是 | 所属小说ID | 1 |"}}}, "url": {"raw": "{{base_url}}/api/relationships"}, "description": "#### 创建关系\n\n**功能说明**: 在两个角色之间建立关系，构建角色关系网络\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话，只能在自己的小说中创建关系\n\n**限流规则**: \n- QPS限制：30次/秒\n- 同用户限制：500个关系/小说\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 源角色和目标角色不能相同 | 选择不同的角色 |\n| 400 | 400 | 角色不属于指定小说 | 检查角色和小说的关联 |\n| 400 | 400 | 关系类型无效 | 选择有效的关系类型 |\n| 403 | 403 | 权限不足 | 只能在自己的小说中创建关系 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 源角色和目标角色必须不同\n- 两个角色必须属于同一小说\n- 关系类型必须存在且用户有权使用\n- 同一对角色间可以有多种不同类型的关系\n- 关系具有方向性（源→目标）"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 1,\n  \"targetId\": 2,\n  \"typeId\": 1,\n  \"description\": \"两人是好朋友\",\n  \"novelId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/relationships"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"sourceId\": 1,\n  \"targetId\": 2,\n  \"typeId\": 1,\n  \"description\": \"这是两个角色之间的重要关系，影响着故事的发展。\",\n  \"novelId\": 1,\n  \"createdAt\": \"2024-01-15T17:30:00.000Z\"\n}", "description": "### 创建关系成功响应\n\n**响应字段**: 包含完整的关系信息\n\n**关系方向性**:\n- sourceId → targetId 表示关系的方向\n- 例如：萧炎(1) → 薰儿(2) 表示萧炎对薰儿的关系\n- 如需双向关系，可创建两个相反方向的关系"}, {"name": "角色相同错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sourceId\": 1,\n  \"targetId\": 1,\n  \"typeId\": 1,\n  \"novelId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/relationships"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Source and target characters cannot be the same\"\n}", "description": "### 角色相同错误响应\n\n**触发条件**:\n- 源角色ID和目标角色ID相同\n- 尝试建立角色与自己的关系\n\n**处理建议**:\n- 选择不同的角色作为关系的两端\n- 检查角色选择逻辑\n- 提供角色选择验证"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "// 检查必要的环境变量", "const requiredVars = ['novel_id', 'character_id', 'relationship_type_id'];", "const missingVars = requiredVars.filter(varName => !pm.environment.get(varName));", "", "if (missingVars.length > 0) {", "    console.log(`错误：缺少必要的环境变量: ${missingVars.join(', ')}`);", "    return;", "}", "", "// 如果没有第二个角色ID，使用第一个角色ID+1作为测试", "if (!pm.environment.get('new_character_id')) {", "    const characterId = parseInt(pm.environment.get('character_id'));", "    pm.environment.set('new_character_id', characterId + 1);", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 403]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('sourceId');", "        pm.expect(jsonData).to.have.property('targetId');", "        pm.expect(jsonData).to.have.property('typeId');", "        pm.expect(jsonData).to.have.property('novelId');", "        pm.expect(jsonData.novelId).to.equal(parseInt(pm.environment.get('novel_id')));", "    });", "    ", "    // 验证关系的方向性", "    pm.test('关系方向性验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.sourceId).to.not.equal(jsonData.targetId);", "    });", "    ", "    // 保存新创建的关系ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_relationship_id', responseJson.id);", "}", "", "// 验证错误响应格式", "if (pm.response.code === 400) {", "    pm.test('验证错误响应格式', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证关系的角色关联", "   - 检查关系类型的有效性", "   - 确认权限控制的正确性", "*/"], "type": "text/javascript"}}]}]}], "variable": [{"key": "base_url", "value": "http://localhost:5001", "description": "API服务器地址"}]}
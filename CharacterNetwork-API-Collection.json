{"info": {"name": "CharacterNetwork API Collection", "description": "## CharacterNetwork API文档\n\n### 项目概述\n- **版本**: v1.0.0\n- **基础URL**: {{base_url}}\n- **认证方式**: Session-based Authentication (Passport.js)\n- **技术栈**: Express.js + TypeScript + PostgreSQL + Drizzle ORM\n\n### 核心功能\n- 用户认证和会话管理\n- 小说信息管理和分类\n- 角色信息管理和关系建立\n- 外部书籍信息检索和缓存\n- 时间线事件和笔记管理\n- 管理员系统管理功能\n\n### 使用说明\n1. **环境变量设置**:\n   - `base_url`: API服务器地址 (默认: http://localhost:5001)\n   - `username`: 测试用户名\n   - `password`: 测试密码\n\n2. **认证流程**:\n   - 首先调用注册或登录接口\n   - 系统会自动设置会话Cookie\n   - 后续请求会自动携带认证信息\n\n3. **测试顺序**:\n   - 建议按模块顺序进行测试\n   - 先完成用户认证\n   - 再进行业务功能测试\n\n### 开发者信息\n- **维护团队**: CharacterNetwork开发团队\n- **联系方式**: <EMAIL>\n- **文档更新**: 2025-06-02 01:45:51", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "认证管理模块", "description": "### 认证管理模块\n\n#### 业务逻辑\n- 基于Session的用户认证机制\n- 使用Passport.js进行身份验证\n- 支持用户注册、登录、登出操作\n- 提供密码修改和用户信息获取功能\n- 会话数据存储在PostgreSQL中\n\n#### 业务约束\n- 用户名和邮箱必须唯一\n- 密码使用scrypt算法加密存储\n- 会话有效期为7天\n- 登录状态通过Cookie维护\n\n#### 权限控制\n- 部分接口需要用户已登录\n- 管理员权限通过isAdmin字段控制", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| username | string | 是 | 用户名，必须唯一 | \"testuser\" |\n| password | string | 是 | 密码，建议8位以上 | \"password123\" |\n| email | string | 是 | 邮箱地址，必须唯一 | \"<EMAIL>\" |"}}}, "url": {"raw": "{{base_url}}/api/register"}, "description": "#### 用户注册\n\n**功能说明**: 创建新用户账户，完成用户注册流程\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同IP限制：5次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | 用户名已存在 | 更换用户名 |\n| 400 | 400 | 邮箱已存在 | 更换邮箱地址 |\n| 400 | 400 | 参数验证失败 | 检查必填参数格式 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户名长度3-20字符，支持字母数字下划线\n- 密码长度至少6位\n- 邮箱格式必须正确\n- 注册成功后自动登录"}, "response": [{"name": "注册成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/register"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=s%3A...; Path=/; HttpOnly"}], "cookie": [], "body": "{\n  \"message\": \"User registered successfully\",\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"isAdmin\": false,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}", "description": "### 注册成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n| user | object | 用户信息对象 |\n| user.id | number | 用户唯一标识 |\n| user.username | string | 用户名 |\n| user.email | string | 邮箱地址 |\n| user.isAdmin | boolean | 是否为管理员 |\n| user.createdAt | string | 创建时间(ISO格式) |"}, {"name": "用户名已存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"existinguser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/register"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"<PERSON><PERSON><PERSON> already exists\"\n}", "description": "### 用户名冲突错误响应\n\n**触发条件**:\n- 提交的用户名已被其他用户使用\n\n**处理建议**:\n- 提示用户更换用户名\n- 可提供用户名可用性检查功能"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 201, 400]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 成功注册时的验证", "if (pm.response.code === 201) {", "    pm.test('注册成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData).to.have.property('user');", "        pm.expect(jsonData.user).to.have.property('id');", "        pm.expect(jsonData.user).to.have.property('username');", "        pm.expect(jsonData.user).to.have.property('email');", "    });", "    ", "    // 保存用户信息到环境变量", "    const responseJson = pm.response.json();", "    pm.environment.set('user_id', responseJson.user.id);", "    pm.environment.set('username', responseJson.user.username);", "}", "", "/* 业务逻辑验证：", "   - 验证用户信息的完整性", "   - 检查返回的用户ID是否为正整数", "   - 验证isAdmin字段默认为false", "*/"], "type": "text/javascript"}}]}, {"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| username | string | 是 | 用户名或邮箱 | \"testuser\" |\n| password | string | 是 | 用户密码 | \"password123\" |"}}}, "url": {"raw": "{{base_url}}/api/login"}, "description": "#### 用户登录\n\n**功能说明**: 验证用户凭据并建立登录会话\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：20次/秒\n- 同IP限制：10次/分钟\n- 失败限制：5次/5分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 用户名或密码错误 | 检查登录凭据 |\n| 400 | 400 | 参数缺失 | 提供用户名和密码 |\n| 429 | 429 | 登录尝试过于频繁 | 等待后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 支持用户名或邮箱登录\n- 密码错误5次后账户临时锁定\n- 登录成功后创建会话Cookie\n- 会话有效期7天"}, "response": [{"name": "登录成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=s%3A...; Path=/; HttpOnly"}], "cookie": [], "body": "{\n  \"message\": \"Login successful\",\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"isAdmin\": false,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}", "description": "### 登录成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n| user | object | 用户信息对象 |\n| user.id | number | 用户唯一标识 |\n| user.username | string | 用户名 |\n| user.email | string | 邮箱地址 |\n| user.isAdmin | boolean | 是否为管理员 |\n| user.createdAt | string | 创建时间(ISO格式) |"}, {"name": "登录失败", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/api/login"}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid username or password\"\n}", "description": "### 登录失败错误响应\n\n**触发条件**:\n- 用户名不存在\n- 密码错误\n- 账户被锁定\n\n**处理建议**:\n- 提示用户检查登录凭据\n- 提供找回密码功能\n- 显示剩余尝试次数"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 401, 400]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 登录成功时的验证", "if (pm.response.code === 200) {", "    pm.test('登录成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData).to.have.property('user');", "        pm.expect(jsonData.user).to.have.property('id');", "    });", "    ", "    // 保存登录状态到环境变量", "    const responseJson = pm.response.json();", "    pm.environment.set('current_user_id', responseJson.user.id);", "    pm.environment.set('is_logged_in', 'true');", "    pm.environment.set('is_admin', responseJson.user.isAdmin);", "    ", "    // 检查是否设置了会话Cookie", "    pm.test('会话Cookie设置验证', function () {", "        pm.expect(pm.response.headers.get('Set-<PERSON><PERSON>')).to.include('connect.sid');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证会话Cookie的正确设置", "   - 检查用户权限信息", "   - 验证登录状态的持久化", "*/"], "type": "text/javascript"}}]}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n无需请求体参数，通过会话Cookie识别用户身份"}}}, "url": {"raw": "{{base_url}}/api/logout"}, "description": "#### 用户登出\n\n**功能说明**: 销毁用户会话，清除登录状态\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：50次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户未登录或会话已过期 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 清除服务器端会话数据\n- 清除客户端会话Cookie\n- 登出后需要重新登录才能访问受保护资源"}, "response": [{"name": "登出成功", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/logout"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT"}], "cookie": [], "body": "{\n  \"message\": \"Logout successful\"\n}", "description": "### 登出成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n\n**Cookie处理**:\n- 会话Cookie被清除\n- 过期时间设置为过去时间"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 登出成功验证", "pm.test('登出成功响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.message).to.include('successful');", "});", "", "// 清除环境变量中的登录状态", "pm.environment.unset('current_user_id');", "pm.environment.set('is_logged_in', 'false');", "pm.environment.unset('is_admin');", "", "/* 业务逻辑验证：", "   - 验证会话Cookie被正确清除", "   - 确认登录状态被重置", "   - 检查后续请求需要重新认证", "*/"], "type": "text/javascript"}}]}, {"name": "获取当前用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}, "description": "#### 获取当前用户信息\n\n**功能说明**: 获取当前登录用户的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只能获取当前登录用户的信息\n- 不返回敏感信息如密码\n- 包含用户权限信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"user\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"isAdmin\": false,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}", "description": "### 获取用户信息成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| user | object | 用户信息对象 |\n| user.id | number | 用户唯一标识 |\n| user.username | string | 用户名 |\n| user.email | string | 邮箱地址 |\n| user.isAdmin | boolean | 是否为管理员 |\n| user.createdAt | string | 创建时间(ISO格式) |"}, {"name": "未登录错误", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Unauthorized\"\n}", "description": "### 未登录错误响应\n\n**触发条件**:\n- 用户未登录\n- 会话已过期\n- 会话Cookie无效\n\n**处理建议**:\n- 重定向到登录页面\n- 提示用户重新登录"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户可能未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 成功获取用户信息时的验证", "if (pm.response.code === 200) {", "    pm.test('用户信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('user');", "        pm.expect(jsonData.user).to.have.property('id');", "        pm.expect(jsonData.user).to.have.property('username');", "        pm.expect(jsonData.user).to.have.property('email');", "        pm.expect(jsonData.user).to.have.property('isAdmin');", "    });", "    ", "    // 验证不包含敏感信息", "    pm.test('敏感信息保护验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.user).to.not.have.property('password');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证用户信息的完整性", "   - 确认敏感信息不被泄露", "   - 检查权限字段的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "修改密码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldpassword123\",\n  \"newPassword\": \"newpassword456\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| currentPassword | string | 是 | 当前密码 | \"oldpassword123\" |\n| newPassword | string | 是 | 新密码，至少6位 | \"newpassword456\" |"}}}, "url": {"raw": "{{base_url}}/api/change-password"}, "description": "#### 修改密码\n\n**功能说明**: 修改当前登录用户的密码\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：5次/秒\n- 同用户限制：3次/小时\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 当前密码错误 | 检查当前密码 |\n| 400 | 400 | 新密码格式不正确 | 密码至少6位 |\n| 429 | 429 | 修改过于频繁 | 等待后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 必须提供正确的当前密码\n- 新密码长度至少6位\n- 新密码不能与当前密码相同\n- 修改成功后不影响当前会话"}, "response": [{"name": "修改成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldpassword123\",\n  \"newPassword\": \"newpassword456\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Password changed successfully\"\n}", "description": "### 密码修改成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| message | string | 操作结果消息 |\n\n**安全说明**:\n- 密码已使用scrypt算法重新加密\n- 当前会话保持有效\n- 建议用户在其他设备重新登录"}, {"name": "当前密码错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"wrongpassword\",\n  \"newPassword\": \"newpassword456\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Current password is incorrect\"\n}", "description": "### 当前密码错误响应\n\n**触发条件**:\n- 提供的当前密码不正确\n\n**处理建议**:\n- 提示用户检查当前密码\n- 提供找回密码功能链接\n- 记录密码错误尝试次数"}, {"name": "新密码格式错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldpassword123\",\n  \"newPassword\": \"123\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"New password must be at least 6 characters long\"\n}", "description": "### 新密码格式错误响应\n\n**触发条件**:\n- 新密码长度不足6位\n- 新密码格式不符合要求\n\n**处理建议**:\n- 显示密码强度要求\n- 提供密码强度检测\n- 建议使用复杂密码"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求将失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 密码修改成功时的验证", "if (pm.response.code === 200) {", "    pm.test('密码修改成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "        pm.expect(jsonData.message).to.include('successful');", "    });", "    ", "    console.log('密码修改成功，建议在其他设备重新登录');", "}", "", "// 错误响应验证", "if (pm.response.code === 400) {", "    pm.test('错误响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证密码修改的安全性", "   - 确认会话状态保持不变", "   - 检查错误信息的准确性", "*/"], "type": "text/javascript"}}]}]}, {"name": "小说类型模块", "description": "### 小说类型模块\n\n#### 业务逻辑\n- 支持用户创建自定义小说类型\n- 提供系统预设的公共类型\n- 类型可设置为公共或私有\n- 支持类型的描述和分类管理\n- 管理员可管理所有类型\n\n#### 业务约束\n- 用户只能管理自己创建的类型\n- 公共类型对所有用户可见\n- 类型名称在用户范围内唯一\n- 删除类型前需检查是否被小说使用\n\n#### 权限控制\n- 需要登录才能访问\n- 用户只能操作自己的类型\n- 管理员拥有全部权限", "item": [{"name": "获取用户小说类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres"}, "description": "#### 获取用户小说类型\n\n**功能说明**: 获取当前用户的所有小说类型，包括用户自定义类型和公共类型\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 返回用户自定义类型和公共类型\n- 按创建时间倒序排列\n- 包含类型的使用统计信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"玄幻\",\n    \"description\": \"以超自然力量为主题的小说类型\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 2,\n    \"name\": \"都市言情\",\n    \"description\": \"现代都市背景的爱情小说\",\n    \"userId\": 1,\n    \"isPublic\": false,\n    \"createdAt\": \"2024-01-15T11:00:00.000Z\"\n  },\n  {\n    \"id\": 3,\n    \"name\": \"科幻\",\n    \"description\": \"科学幻想类小说\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n]", "description": "### 获取小说类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 类型唯一标识 |\n| name | string | 类型名称 |\n| description | string | 类型描述 |\n| userId | number | 创建者ID，0表示系统类型 |\n| isPublic | boolean | 是否为公共类型 |\n| createdAt | string | 创建时间(ISO格式) |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    ", "    if (jsonData.length > 0) {", "        pm.expect(jsonData[0]).to.have.property('id');", "        pm.expect(jsonData[0]).to.have.property('name');", "        pm.expect(jsonData[0]).to.have.property('userId');", "        pm.expect(jsonData[0]).to.have.property('isPublic');", "    }", "});", "", "// 保存第一个类型ID用于后续测试", "const responseJson = pm.response.json();", "if (responseJson.length > 0) {", "    pm.environment.set('genre_id', responseJson[0].id);", "}", "", "/* 业务逻辑验证：", "   - 验证返回的类型包含公共类型和用户类型", "   - 检查类型数据的完整性", "   - 确认权限字段的正确性", "*/"], "type": "text/javascript"}}]}, {"name": "获取公共小说类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/public"}, "description": "#### 获取公共小说类型\n\n**功能说明**: 获取所有公共小说类型，无需登录即可访问\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回isPublic为true的类型\n- 按名称字母顺序排列\n- 包含系统预设类型"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/public"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"玄幻\",\n    \"description\": \"以超自然力量为主题的小说类型\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 3,\n    \"name\": \"科幻\",\n    \"description\": \"科学幻想类小说\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  },\n  {\n    \"id\": 5,\n    \"name\": \"武侠\",\n    \"description\": \"中国传统武侠小说\",\n    \"userId\": 0,\n    \"isPublic\": true,\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n]", "description": "### 获取公共类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 类型唯一标识 |\n| name | string | 类型名称 |\n| description | string | 类型描述 |\n| userId | number | 创建者ID，0表示系统类型 |\n| isPublic | boolean | 是否为公共类型(均为true) |\n| createdAt | string | 创建时间(ISO格式) |"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.response.to.have.status(200);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 响应格式验证", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// 验证所有返回的类型都是公共类型", "pm.test('公共类型验证', function () {", "    const jsonData = pm.response.json();", "    jsonData.forEach(genre => {", "        pm.expect(genre.isPublic).to.be.true;", "    });", "});", "", "/* 业务逻辑验证：", "   - 验证只返回公共类型", "   - 检查类型数据的完整性", "   - 确认无需登录即可访问", "*/"], "type": "text/javascript"}}]}, {"name": "获取特定小说类型", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/{{genre_id}}", "variable": [{"key": "genre_id", "value": "1", "description": "小说类型ID"}]}, "description": "#### 获取特定小说类型\n\n**功能说明**: 根据ID获取特定小说类型的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：200次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 类型不存在 | 检查类型ID是否正确 |\n| 403 | 403 | 权限不足 | 只能访问自己的或公共类型 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户只能查看自己创建的类型或公共类型\n- 管理员可以查看所有类型\n- 返回类型的详细信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"name\": \"玄幻\",\n  \"description\": \"以超自然力量为主题的小说类型，包含修仙、魔法、异能等元素\",\n  \"userId\": 0,\n  \"isPublic\": true,\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取类型详情成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 类型唯一标识 |\n| name | string | 类型名称 |\n| description | string | 类型详细描述 |\n| userId | number | 创建者ID，0表示系统类型 |\n| isPublic | boolean | 是否为公共类型 |\n| createdAt | string | 创建时间(ISO格式) |"}, {"name": "类型不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/genres/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"<PERSON>re not found\"\n}", "description": "### 类型不存在错误响应\n\n**触发条件**:\n- 提供的类型ID不存在\n- 类型已被删除\n\n**处理建议**:\n- 检查类型ID是否正确\n- 刷新类型列表"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有类型ID", "if (!pm.environment.get('genre_id')) {", "    console.log('警告：未设置genre_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 403, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('类型详情响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('description');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData).to.have.property('isPublic');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证类型信息的完整性", "   - 检查权限控制的正确性", "   - 确认数据格式的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "创建小说类型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"悬疑推理\",\n  \"description\": \"以推理解谜为主要情节的小说类型\",\n  \"isPublic\": false\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| name | string | 是 | 类型名称，用户范围内唯一 | \"悬疑推理\" |\n| description | string | 否 | 类型描述 | \"以推理解谜为主要情节的小说类型\" |\n| isPublic | boolean | 否 | 是否为公共类型，默认false | false |"}}}, "url": {"raw": "{{base_url}}/api/genres"}, "description": "#### 创建小说类型\n\n**功能说明**: 创建新的小说类型，可设置为公共或私有\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：10次/秒\n- 同用户限制：20个/小时\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 类型名称已存在 | 更换类型名称 |\n| 400 | 400 | 参数验证失败 | 检查必填参数 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 类型名称在用户范围内必须唯一\n- 普通用户创建的类型默认为私有\n- 管理员可以创建公共类型\n- 类型名称长度1-50字符"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"悬疑推理\",\n  \"description\": \"以推理解谜为主要情节的小说类型\",\n  \"isPublic\": false\n}"}, "url": {"raw": "{{base_url}}/api/genres"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"name\": \"悬疑推理\",\n  \"description\": \"以推理解谜为主要情节的小说类型\",\n  \"userId\": 1,\n  \"isPublic\": false,\n  \"createdAt\": \"2024-01-15T12:00:00.000Z\"\n}", "description": "### 创建类型成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 新创建的类型ID |\n| name | string | 类型名称 |\n| description | string | 类型描述 |\n| userId | number | 创建者ID |\n| isPublic | boolean | 是否为公共类型 |\n| createdAt | string | 创建时间(ISO格式) |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData.userId).to.equal(parseInt(pm.environment.get('current_user_id')));", "    });", "    ", "    // 保存新创建的类型ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_genre_id', responseJson.id);", "}", "", "/* 业务逻辑验证：", "   - 验证创建者ID的正确性", "   - 检查类型属性的设置", "   - 确认权限控制的有效性", "*/"], "type": "text/javascript"}}]}]}, {"name": "书籍信息模块", "description": "### 书籍信息模块\n\n#### 业务逻辑\n- 从外部API获取书籍信息\n- 缓存书籍信息到本地数据库\n- 支持书籍信息的搜索和检索\n- 提供书籍信息的CRUD操作\n- 关联小说与外部书籍信息\n\n#### 业务约束\n- 外部ID在系统中唯一\n- 书籍信息支持多种数据源\n- 管理员可管理所有书籍信息\n- 普通用户只能查看和搜索\n\n#### 权限控制\n- 查看和搜索需要登录\n- 创建和更新需要管理员权限\n- 删除操作仅限管理员", "item": [{"name": "获取书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/{{book_id}}", "variable": [{"key": "book_id", "value": "1", "description": "书籍信息ID"}]}, "description": "#### 获取书籍信息\n\n**功能说明**: 根据ID获取特定书籍的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：100次/秒\n- 无特殊限制\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 书籍不存在 | 检查书籍ID是否正确 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 返回完整的书籍信息\n- 包含外部API的原始数据\n- 显示书籍的使用统计"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"externalId\": \"weread_12345\",\n  \"title\": \"斗破苍穹\",\n  \"author\": \"天蚕土豆\",\n  \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n  \"coverImage\": \"https://example.com/cover.jpg\",\n  \"publishedDate\": \"2009-04-14\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 5000000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取书籍信息成功响应\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 书籍信息ID |\n| externalId | string | 外部API的唯一标识 |\n| title | string | 书籍标题 |\n| author | string | 作者 |\n| description | string | 书籍描述 |\n| coverImage | string | 封面图片URL |\n| publishedDate | string | 发布日期 |\n| publisher | string | 出版商 |\n| isbn | string | ISBN号码 |\n| pageCount | number | 页数/字数 |\n| categories | array | 分类标签 |\n| language | string | 语言代码 |\n| createdAt | string | 创建时间 |\n| updatedAt | string | 更新时间 |"}, {"name": "书籍不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/999"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Book not found\"\n}", "description": "### 书籍不存在错误响应\n\n**触发条件**:\n- 提供的书籍ID不存在\n- 书籍信息已被删除\n\n**处理建议**:\n- 检查书籍ID是否正确\n- 尝试重新搜索书籍"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有书籍ID", "if (!pm.environment.get('book_id')) {", "    console.log('警告：未设置book_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('书籍信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('author');", "    });", "    ", "    // 保存书籍信息用于后续测试", "    const responseJson = pm.response.json();", "    pm.environment.set('book_external_id', responseJson.externalId);", "}", "", "/* 业务逻辑验证：", "   - 验证书籍信息的完整性", "   - 检查外部ID的格式", "   - 确认数据结构的一致性", "*/"], "type": "text/javascript"}}]}, {"name": "通过外部ID获取书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/{{external_id}}", "variable": [{"key": "external_id", "value": "weread_12345", "description": "外部API的书籍ID"}]}, "description": "#### 通过外部ID获取书籍信息\n\n**功能说明**: 根据外部API的ID获取书籍信息，如果本地不存在则从外部API获取\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：50次/秒\n- 外部API调用限制：20次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 404 | 404 | 外部书籍不存在 | 检查外部ID是否正确 |\n| 503 | 503 | 外部API不可用 | 稍后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 优先返回本地缓存的数据\n- 本地不存在时调用外部API\n- 自动缓存外部API返回的数据\n- 支持多种外部数据源"}, "response": [{"name": "获取成功(本地缓存)", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/weread_12345"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Data-Source", "value": "cache"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"externalId\": \"weread_12345\",\n  \"title\": \"斗破苍穹\",\n  \"author\": \"天蚕土豆\",\n  \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n  \"coverImage\": \"https://example.com/cover.jpg\",\n  \"publishedDate\": \"2009-04-14\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 5000000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T10:30:00.000Z\"\n}", "description": "### 获取成功响应(本地缓存)\n\n**响应头说明**:\n- X-Data-Source: cache 表示数据来源于本地缓存\n\n**响应字段**: 与获取书籍信息接口相同"}, {"name": "获取成功(外部API)", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/external/weread_67890"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Data-Source", "value": "external"}], "cookie": [], "body": "{\n  \"id\": 2,\n  \"externalId\": \"weread_67890\",\n  \"title\": \"完美世界\",\n  \"author\": \"辰东\",\n  \"description\": \"一粒尘可填海，一根草斩尽日月星辰，弹指间天翻地覆。\",\n  \"coverImage\": \"https://example.com/cover2.jpg\",\n  \"publishedDate\": \"2013-08-16\",\n  \"publisher\": \"起点中文网\",\n  \"isbn\": null,\n  \"pageCount\": 6800000,\n  \"categories\": [\"玄幻\", \"异世大陆\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T12:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T12:30:00.000Z\"\n}", "description": "### 获取成功响应(外部API)\n\n**响应头说明**:\n- X-Data-Source: external 表示数据来源于外部API\n\n**业务说明**:\n- 数据已自动缓存到本地数据库\n- 下次访问将直接返回缓存数据"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 检查是否有外部ID", "if (!pm.environment.get('book_external_id')) {", "    console.log('警告：未设置book_external_id环境变量');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 404, 503, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证(外部API调用可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "// 成功获取时的验证", "if (pm.response.code === 200) {", "    pm.test('书籍信息响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "    });", "    ", "    // 检查数据源", "    const dataSource = pm.response.headers.get('X-Data-Source');", "    if (dataSource) {", "        pm.test('数据源标识验证', function () {", "            pm.expect(['cache', 'external']).to.include(dataSource);", "        });", "        console.log(`数据来源: ${dataSource}`);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证数据源的正确标识", "   - 检查外部API集成的稳定性", "   - 确认缓存机制的有效性", "*/"], "type": "text/javascript"}}]}, {"name": "搜索书籍信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/{{search_query}}?page=1&limit=10", "variable": [{"key": "search_query", "value": "斗破苍穹", "description": "搜索关键词"}], "query": [{"key": "page", "value": "1", "description": "页码，从1开始"}, {"key": "limit", "value": "10", "description": "每页数量，最大50"}]}, "description": "#### 搜索书籍信息\n\n**功能说明**: 根据关键词搜索书籍信息，支持本地搜索和外部API搜索\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效会话\n\n**限流规则**: \n- QPS限制：30次/秒\n- 外部搜索限制：10次/分钟\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 400 | 400 | 搜索关键词为空 | 提供有效的搜索关键词 |\n| 503 | 503 | 外部搜索服务不可用 | 稍后重试 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 关键词长度至少2个字符\n- 优先搜索本地缓存数据\n- 本地无结果时调用外部API\n- 支持分页查询\n- 自动缓存搜索结果"}, "response": [{"name": "搜索成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/斗破苍穹?page=1&limit=10"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Total-Count", "value": "3"}], "cookie": [], "body": "{\n  \"books\": [\n    {\n      \"id\": 1,\n      \"externalId\": \"weread_12345\",\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"description\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n      \"coverImage\": \"https://example.com/cover.jpg\",\n      \"publishedDate\": \"2009-04-14\",\n      \"categories\": [\"玄幻\", \"异世大陆\"]\n    },\n    {\n      \"id\": null,\n      \"externalId\": \"weread_54321\",\n      \"title\": \"斗破苍穹之大主宰\",\n      \"author\": \"天蚕土豆\",\n      \"description\": \"斗破苍穹续作，讲述萧炎之后的故事\",\n      \"coverImage\": \"https://example.com/cover3.jpg\",\n      \"publishedDate\": \"2013-07-12\",\n      \"categories\": [\"玄幻\"]\n    }\n  ],\n  \"pagination\": {\n    \"page\": 1,\n    \"limit\": 10,\n    \"total\": 2,\n    \"totalPages\": 1\n  },\n  \"searchSource\": \"mixed\"\n}", "description": "### 搜索成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| books | array | 书籍信息列表 |\n| books[].id | number/null | 本地书籍ID，null表示仅来自外部 |\n| books[].externalId | string | 外部API的唯一标识 |\n| books[].title | string | 书籍标题 |\n| books[].author | string | 作者 |\n| books[].description | string | 书籍描述 |\n| books[].coverImage | string | 封面图片URL |\n| books[].publishedDate | string | 发布日期 |\n| books[].categories | array | 分类标签 |\n| pagination | object | 分页信息 |\n| pagination.page | number | 当前页码 |\n| pagination.limit | number | 每页数量 |\n| pagination.total | number | 总记录数 |\n| pagination.totalPages | number | 总页数 |\n| searchSource | string | 搜索来源(local/external/mixed) |"}, {"name": "搜索关键词为空", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/books/search/?page=1&limit=10"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Search query cannot be empty\"\n}", "description": "### 搜索关键词为空错误响应\n\n**触发条件**:\n- 未提供搜索关键词\n- 搜索关键词长度不足\n\n**处理建议**:\n- 提示用户输入有效的搜索关键词\n- 关键词长度至少2个字符"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('警告：用户未登录，此请求可能失败');", "}", "", "// 设置默认搜索关键词", "if (!pm.environment.get('search_query')) {", "    pm.environment.set('search_query', '斗破苍穹');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 503, 401]).to.include(pm.response.code);", "});", "", "// 响应时间验证(搜索可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(15000);", "});", "", "// 搜索成功时的验证", "if (pm.response.code === 200) {", "    pm.test('搜索结果响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('books');", "        pm.expect(jsonData).to.have.property('pagination');", "        pm.expect(jsonData).to.have.property('searchSource');", "        pm.expect(jsonData.books).to.be.an('array');", "    });", "    ", "    // 验证分页信息", "    pm.test('分页信息验证', function () {", "        const jsonData = pm.response.json();", "        const pagination = jsonData.pagination;", "        pm.expect(pagination).to.have.property('page');", "        pm.expect(pagination).to.have.property('limit');", "        pm.expect(pagination).to.have.property('total');", "        pm.expect(pagination).to.have.property('totalPages');", "    });", "    ", "    // 保存第一个搜索结果", "    const responseJson = pm.response.json();", "    if (responseJson.books.length > 0) {", "        pm.environment.set('search_result_external_id', responseJson.books[0].externalId);", "    }", "}", "", "/* 业务逻辑验证：", "   - 验证搜索结果的完整性", "   - 检查分页功能的正确性", "   - 确认搜索来源的标识", "*/"], "type": "text/javascript"}}]}, {"name": "创建书籍信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\",\n  \"coverImage\": \"https://example.com/custom-cover.jpg\",\n  \"publishedDate\": \"2024-01-15\",\n  \"publisher\": \"自定义出版社\",\n  \"isbn\": \"978-0000000000\",\n  \"pageCount\": 300000,\n  \"categories\": [\"原创\", \"都市\"],\n  \"language\": \"zh-CN\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| externalId | string | 是 | 外部唯一标识 | \"custom_001\" |\n| title | string | 是 | 书籍标题 | \"自定义小说\" |\n| author | string | 是 | 作者 | \"作者名\" |\n| description | string | 否 | 书籍描述 | \"这是一本自定义添加的小说\" |\n| coverImage | string | 否 | 封面图片URL | \"https://example.com/cover.jpg\" |\n| publishedDate | string | 否 | 发布日期 | \"2024-01-15\" |\n| publisher | string | 否 | 出版商 | \"自定义出版社\" |\n| isbn | string | 否 | ISBN号码 | \"978-0000000000\" |\n| pageCount | number | 否 | 页数/字数 | 300000 |\n| categories | array | 否 | 分类标签 | [\"原创\", \"都市\"] |\n| language | string | 否 | 语言代码 | \"zh-CN\" |"}}}, "url": {"raw": "{{base_url}}/api/books"}, "description": "#### 创建书籍信息\n\n**功能说明**: 手动创建书籍信息，通常用于添加外部API不支持的书籍\n\n**权限要求**: \n- 角色：管理员\n- 权限：需要管理员权限\n\n**限流规则**: \n- QPS限制：5次/秒\n- 同用户限制：50个/天\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 未登录 | 用户需要先登录 |\n| 403 | 403 | 权限不足 | 需要管理员权限 |\n| 400 | 400 | 外部ID已存在 | 更换外部ID |\n| 400 | 400 | 参数验证失败 | 检查必填参数 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 外部ID在系统中必须唯一\n- 标题和作者为必填字段\n- 管理员可以创建任意书籍信息\n- 创建后可被用户搜索和使用"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\"\n}"}, "url": {"raw": "{{base_url}}/api/books"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 10,\n  \"externalId\": \"custom_001\",\n  \"title\": \"自定义小说\",\n  \"author\": \"作者名\",\n  \"description\": \"这是一本自定义添加的小说\",\n  \"coverImage\": \"https://example.com/custom-cover.jpg\",\n  \"publishedDate\": \"2024-01-15\",\n  \"publisher\": \"自定义出版社\",\n  \"isbn\": \"978-0000000000\",\n  \"pageCount\": 300000,\n  \"categories\": [\"原创\", \"都市\"],\n  \"language\": \"zh-CN\",\n  \"createdAt\": \"2024-01-15T14:30:00.000Z\",\n  \"updatedAt\": \"2024-01-15T14:30:00.000Z\"\n}", "description": "### 创建书籍信息成功响应\n\n**响应字段**: 包含完整的书籍信息，与获取书籍信息接口相同"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查登录状态和管理员权限", "if (pm.environment.get('is_logged_in') !== 'true') {", "    console.log('错误：用户未登录，此请求将失败');", "    return;", "}", "", "if (pm.environment.get('is_admin') !== 'true') {", "    console.log('错误：需要管理员权限，此请求将失败');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([201, 400, 401, 403]).to.include(pm.response.code);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 创建成功时的验证", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('externalId');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('author');", "    });", "    ", "    // 保存新创建的书籍ID", "    const responseJson = pm.response.json();", "    pm.environment.set('new_book_id', responseJson.id);", "}", "", "/* 业务逻辑验证：", "   - 验证管理员权限的控制", "   - 检查书籍信息的完整性", "   - 确认外部ID的唯一性", "*/"], "type": "text/javascript"}}]}]}, {"name": "微信读书代理模块", "description": "### 微信读书代理模块\n\n#### 业务逻辑\n- 代理微信读书API的搜索请求\n- 解决跨域访问问题\n- 提供统一的搜索接口\n- 处理外部API的错误和异常\n- 格式化返回数据结构\n\n#### 业务约束\n- 无需用户登录即可访问\n- 受外部API限流限制\n- 搜索关键词不能为空\n- 依赖外部服务的可用性\n\n#### 权限控制\n- 公开接口，无需认证\n- 基于IP的访问频率限制\n- 防止恶意请求和滥用", "item": [{"name": "微信读书搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword={{search_keyword}}", "query": [{"key": "keyword", "value": "{{search_keyword}}", "description": "搜索关键词"}]}, "description": "#### 微信读书搜索\n\n**功能说明**: 通过微信读书API搜索书籍信息，代理外部请求解决跨域问题\n\n**权限要求**: \n- 角色：无需登录\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：20次/秒\n- 同IP限制：100次/小时\n- 外部API限制：依赖微信读书API\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | 搜索关键词不能为空 | 提供有效的搜索关键词 |\n| 503 | 503 | 微信读书API不可用 | 稍后重试 |\n| 500 | 500 | 微信读书API返回非JSON响应 | 检查外部API状态 |\n| 429 | 429 | 请求过于频繁 | 降低请求频率 |\n| 502 | 502 | 外部API错误 | 联系技术支持 |\n\n**业务规则**:\n- 搜索关键词长度至少1个字符\n- 自动处理外部API的响应格式\n- 返回标准化的书籍信息结构\n- 支持中文和英文搜索\n- 实时搜索，不缓存结果"}, "response": [{"name": "搜索成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=斗破苍穹"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Proxy-Source", "value": "weread"}], "cookie": [], "body": "{\n  \"books\": [\n    {\n      \"bookId\": \"12345\",\n      \"title\": \"斗破苍穹\",\n      \"author\": \"天蚕土豆\",\n      \"cover\": \"https://weread.qq.com/cover/12345.jpg\",\n      \"intro\": \"这里是斗气大陆，没有花俏的魔法，有的，仅仅是繁衍到巅峰的斗气！\",\n      \"category\": \"玄幻\",\n      \"publishTime\": \"2009-04-14\",\n      \"publisher\": \"起点中文网\",\n      \"isbn\": \"\",\n      \"totalWords\": 5000000,\n      \"finished\": 1,\n      \"payType\": 0\n    },\n    {\n      \"bookId\": \"67890\",\n      \"title\": \"斗破苍穹之大主宰\",\n      \"author\": \"天蚕土豆\",\n      \"cover\": \"https://weread.qq.com/cover/67890.jpg\",\n      \"intro\": \"斗破苍穹续作，讲述萧炎之后的故事\",\n      \"category\": \"玄幻\",\n      \"publishTime\": \"2013-07-12\",\n      \"publisher\": \"起点中文网\",\n      \"isbn\": \"\",\n      \"totalWords\": 4200000,\n      \"finished\": 1,\n      \"payType\": 1\n    }\n  ],\n  \"totalCount\": 2,\n  \"hasMore\": false\n}", "description": "### 搜索成功响应说明\n\n**响应头说明**:\n- X-Proxy-Source: weread 表示数据来源于微信读书API\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| books | array | 书籍信息列表 |\n| books[].bookId | string | 微信读书的书籍ID |\n| books[].title | string | 书籍标题 |\n| books[].author | string | 作者 |\n| books[].cover | string | 封面图片URL |\n| books[].intro | string | 书籍简介 |\n| books[].category | string | 分类 |\n| books[].publishTime | string | 发布时间 |\n| books[].publisher | string | 出版商 |\n| books[].isbn | string | ISBN号码 |\n| books[].totalWords | number | 总字数 |\n| books[].finished | number | 是否完结(0未完结,1完结) |\n| books[].payType | number | 付费类型(0免费,1付费) |\n| totalCount | number | 搜索结果总数 |\n| hasMore | boolean | 是否有更多结果 |"}, {"name": "搜索关键词为空", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword="}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"搜索关键词不能为空\"\n}", "description": "### 搜索关键词为空错误响应\n\n**触发条件**:\n- 未提供keyword参数\n- keyword参数值为空字符串\n- keyword参数只包含空格\n\n**处理建议**:\n- 提示用户输入有效的搜索关键词\n- 前端进行输入验证\n- 提供搜索建议或热门关键词"}, {"name": "微信读书API不可用", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=测试"}}, "status": "Service Unavailable", "code": 503, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"微信读书API错误: 503\"\n}", "description": "### 外部API不可用错误响应\n\n**触发条件**:\n- 微信读书API服务器故障\n- 网络连接问题\n- 外部API返回错误状态码\n\n**处理建议**:\n- 稍后重试请求\n- 检查网络连接\n- 使用其他数据源作为备选\n- 显示友好的错误提示"}, {"name": "外部API返回非JSON响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=特殊字符"}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"微信读书API返回非JSON响应\",\n  \"contentType\": \"text/html\"\n}", "description": "### 外部API响应格式错误\n\n**触发条件**:\n- 外部API返回HTML页面而非JSON\n- 外部API响应格式异常\n- 搜索关键词包含特殊字符导致错误\n\n**处理建议**:\n- 检查搜索关键词的合法性\n- 联系技术支持检查外部API状态\n- 使用备用搜索方案"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 设置默认搜索关键词", "if (!pm.environment.get('search_keyword')) {", "    pm.environment.set('search_keyword', '斗破苍穹');", "}", "", "// 记录请求开始时间", "pm.environment.set('request_start_time', new Date().getTime());"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect([200, 400, 503, 500]).to.include(pm.response.code);", "});", "", "// 响应时间验证(外部API调用可能较慢)", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(15000);", "});", "", "// 搜索成功时的验证", "if (pm.response.code === 200) {", "    pm.test('搜索结果响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('books');", "        pm.expect(jsonData).to.have.property('totalCount');", "        pm.expect(jsonData).to.have.property('hasMore');", "        pm.expect(jsonData.books).to.be.an('array');", "    });", "    ", "    // 验证书籍信息结构", "    const responseJson = pm.response.json();", "    if (responseJson.books.length > 0) {", "        pm.test('书籍信息结构验证', function () {", "            const book = responseJson.books[0];", "            pm.expect(book).to.have.property('bookId');", "            pm.expect(book).to.have.property('title');", "            pm.expect(book).to.have.property('author');", "            pm.expect(book).to.have.property('cover');", "        });", "        ", "        // 保存第一个搜索结果的bookId", "        pm.environment.set('weread_book_id', responseJson.books[0].bookId);", "    }", "    ", "    // 检查代理来源标识", "    const proxySource = pm.response.headers.get('X-Proxy-Source');", "    if (proxySource) {", "        pm.test('代理来源标识验证', function () {", "            pm.expect(proxySource).to.equal('weread');", "        });", "    }", "}", "", "// 错误响应验证", "if (pm.response.code >= 400) {", "    pm.test('错误响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}", "", "// 计算实际响应时间", "const startTime = pm.environment.get('request_start_time');", "if (startTime) {", "    const actualResponseTime = new Date().getTime() - parseInt(startTime);", "    console.log(`实际响应时间: ${actualResponseTime}ms`);", "}", "", "/* 业务逻辑验证：", "   - 验证外部API代理的稳定性", "   - 检查数据格式的标准化", "   - 确认错误处理的完整性", "   - 监控外部服务的可用性", "*/"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"exec": ["// 全局预请求脚本", "// 设置请求时间戳", "pm.globals.set('request_timestamp', new Date().getTime());", "", "// 检查基础URL设置", "if (!pm.environment.get('base_url') && !pm.globals.get('base_url')) {", "    console.log('警告：未设置base_url，使用默认值');", "    pm.globals.set('base_url', 'http://localhost:5001');", "}", "", "// 设置通用请求头", "pm.request.headers.add({", "    key: 'User-Agent',", "    value: 'CharacterNetwork-Postman-Collection/1.0.0'", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 全局测试脚本", "// 记录API调用日志", "const requestTime = pm.globals.get('request_timestamp');", "const responseTime = new Date().getTime();", "const duration = responseTime - parseInt(requestTime);", "", "console.log(`API调用: ${pm.request.method} ${pm.request.url} - ${pm.response.code} (${duration}ms)`);", "", "// 通用响应验证", "pm.test('响应Content-Type验证', function () {", "    const contentType = pm.response.headers.get('Content-Type');", "    if (contentType) {", "        pm.expect(contentType).to.include('application/json');", "    }", "});", "", "// 错误响应统一处理", "if (pm.response.code >= 400) {", "    pm.test('错误响应包含message字段', function () {", "        try {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData).to.have.property('message');", "        } catch (e) {", "            console.log('响应不是有效的JSON格式');", "        }", "    });", "}"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:5001", "description": "API服务器基础URL，根据环境切换（开发/测试/生产）"}, {"key": "api_version", "value": "v1", "description": "API版本号"}, {"key": "timeout", "value": "10000", "description": "请求超时时间(毫秒)"}]}